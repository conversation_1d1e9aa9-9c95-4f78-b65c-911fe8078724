<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
    <property name="APP" value="genn-ai-hub" />
    <property name="LOG_HOME" value="/appdata/applogs" />
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{yyyy-MM-dd.HH:mm:ss.SSS} [${APP}] [%thread] [%X{tid}] %-5p %c{0}.%method[%line] - %m%n</pattern>
            </layout>
        </encoder>
    </appender>
    <appender name="DETAIL" class="ch.qos.logback.core.rolling.RollingFileAppender" additivity="false">
        <File>${LOG_HOME}/${APP}_detail.log</File>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{yyyy-MM-dd.HH:mm:ss.SSS} [${APP}] [%thread] [%X{tid}] %-5p %c{0}.%method[%line] - %m%n</pattern>
            </layout>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP}_detail.log.%d{yyyyMMddHH}</fileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>20</maxHistory>
        </rollingPolicy>
    </appender>


    <appender name="GRPC-LOG" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{yyyy-MM-dd.HH:mm:ss.SSS} [${APP}] [%thread] [%X{tid}] %-5p %c{0}.%method[%line] - %m%n</pattern>
            </layout>
        </encoder>
    </appender>

    <logger name="org.springframework" level="WARN"/>

    <root level="INFO">
        <appender-ref ref="DETAIL"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
