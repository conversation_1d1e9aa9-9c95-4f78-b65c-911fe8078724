<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionAnalysisMapper">

    <select id="getAnalysis" resultType="cn.genn.ai.hub.app.application.dto.question.QuestionAnalysisDTO">
        select
        t.concise as concise,
        t.type as type,
        sum(t.count) as count
        from question_analysis t
        <where>
            t.app_id = #{query.appId}
            <if test="query.sources != null">
                and t.source in
                <foreach collection="query.sources" item="source" open="(" separator="," close=")">
                    #{source}
                </foreach>
            </if>
        </where>
        group by type
        order by count desc
        limit #{query.top};
    </select>

    <select id="getAllTypes" resultType="java.lang.String">
        SELECT
        t.type as type
        FROM question_analysis t
        <where>
            <if test="appId != null and appId != ''">
                and t.app_id = #{appId}
            </if>
        </where>
        group by t.type;
    </select>
</mapper>
