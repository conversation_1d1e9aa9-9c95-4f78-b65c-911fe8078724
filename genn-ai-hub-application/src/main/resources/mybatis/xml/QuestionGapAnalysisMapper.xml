<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionGapAnalysisMapper">

    <select id="getAllTypes" resultType="java.lang.String">
        SELECT
        t.type as type
        FROM question_gap_analysis t
        <where>
            <if test="appId != null and appId != ''">
                and t.app_id = #{appId}
            </if>
        </where>
        group by t.type;
    </select>
</mapper>
