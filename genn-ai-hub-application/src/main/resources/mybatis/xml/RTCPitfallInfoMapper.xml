<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.ai.hub.app.infrastructure.repository.mapper.RTCPitfallInfoMapper">
    <resultMap id="PitfallInfoMap" type="cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallPageDTO">
        <id property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="tenantId" column="tenantId"/>
        <result property="createUserId" column="createUserId"/>
        <result property="pitfallArea" column="pitfallArea"/>
        <result property="submitTime" column="submitTime"/>
        <result property="confirmTime" column="confirmTime"/>
        <result property="resolveTime" column="resolveTime"/>
        <result property="createUserName" column="createUserName"/>
        <result property="pitfallType" column="pitfallType"/>
        <result property="pitfallContent" column="pitfallContent"
                javaType="cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallContentDTO"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <select id="selectPitfallPage" resultMap="PitfallInfoMap">
        SELECT
        id,
        STATUS,
        tenantId,
        pitfallArea,
        submitTime,
        confirmTime,
        resolveTime,
        createUserName,
        pitfallType,
        pitfallContent
        FROM
        (
        SELECT
        t1.id,
        t1.STATUS,
        t1.tenant_id AS tenantId,
        t1.pitfall_area AS pitfallArea,
        t1.submit_time AS submitTime,
        t1.confirm_time AS confirmTime,
        t1.resolve_time AS resolveTime,
        t1.create_user_name AS createUserName,
        t2.pitfall_type AS pitfallType,
        t2.pitfall_content AS pitfallContent,
        ROW_NUMBER() OVER ( PARTITION BY t1.id ORDER BY t2.pitfall_type DESC, t2.id ASC ) AS rn
        FROM
        rtc_pitfall_info t1
        LEFT JOIN rtc_pitfall_item t2 ON t1.id = t2.pitfall_id
        WHERE
        t1.deleted = 0 AND t2.deleted = 0
        <if test="query.status != null">
            AND t1.status = #{query.status}
        </if>
        <if test="query.createUserId != null">
            AND t1.create_user_id = #{query.createUserId}
        </if>
        ) a
        WHERE
        rn = 1
        ORDER BY id DESC
    </select>
    <select id="selectSubmitPitfallList" resultMap="PitfallInfoMap">
        SELECT
        t1.id,
        t1.STATUS,
        t1.tenant_id AS tenantId,
        t1.create_user_id AS createUserId,
        t1.pitfall_area AS pitfallArea,
        t1.submit_time AS submitTime,
        t1.confirm_time AS confirmTime,
        t1.resolve_time AS resolveTime,
        t1.create_user_name AS createUserName,
        t2.pitfall_type AS pitfallType,
        t2.pitfall_content AS pitfallContent
        FROM
        rtc_pitfall_info t1
        LEFT JOIN rtc_pitfall_item t2 ON t1.id = t2.pitfall_id
        where t1.deleted = 0 and t2.deleted = 0
        AND t1.status != 1
        <if test="query.createTimeIndex != null">
            and t1.create_time >= #{query.createTimeIndex}
        </if>
        <if test="query.createTimeEnd != null">
            and t1.create_time <![CDATA[ <= ]]> #{query.createTimeEnd}
        </if>
        <if test="query.createUserId != null">
            AND t1.create_user_id = #{query.createUserId}
        </if>
    </select>
</mapper>
