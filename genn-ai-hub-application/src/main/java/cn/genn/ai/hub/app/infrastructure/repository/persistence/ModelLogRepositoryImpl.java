package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.ModelLogAssembler;
import cn.genn.ai.hub.app.application.dto.request.ModelLogQuery;
import cn.genn.ai.hub.app.application.dto.response.ModelLogDTO;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ModelLogMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelLogPO;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * 运单仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class ModelLogRepositoryImpl extends ServiceImpl<ModelLogMapper, ModelLogPO> {

    @Resource
    private ModelLogMapper logMapper;

    @Resource
    private ModelLogAssembler logAssembler;


    public PageResultDTO<ModelLogDTO> logPage(ModelLogQuery query) {
        LambdaQueryWrapper<ModelLogPO> queryWrapper = buildQueryWrapper(query);
        return logAssembler.toPageResult(logMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
    }

    private LambdaQueryWrapper<ModelLogPO> buildQueryWrapper(ModelLogQuery query) {
        return new QueryWrapper<ModelLogPO>()
            .lambda()
            .eq(StringUtils.isNotEmpty(query.getModelKey()), ModelLogPO::getModelKey, query.getModelKey())
            .eq(StringUtils.isNotEmpty(query.getModel()), ModelLogPO::getModel, query.getModel())
            .eq(StringUtils.isNotEmpty(query.getTraceId()), ModelLogPO::getTraceId, query.getTraceId())
            .eq(StringUtils.isNotEmpty(query.getRequestSource()), ModelLogPO::getRequestSource, query.getRequestSource())
            .ge(Objects.nonNull(query.getBeginTime()), ModelLogPO::getRequestTime, query.getBeginTime())
            .le(Objects.nonNull(query.getEndTime()), ModelLogPO::getRequestTime, query.getEndTime())
            .gt(Objects.nonNull(query.getCostTime()) && query.getCostTime() > 0L, ModelLogPO::getCostTime, query.getCostTime())
            .eq(Objects.nonNull(query.getSuccess()), ModelLogPO::getSuccess, query.getSuccess())
            .orderByDesc(ModelLogPO::getId);
    }
}
