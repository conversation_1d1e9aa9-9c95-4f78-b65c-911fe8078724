package cn.genn.ai.hub.app.infrastructure.external.agent;

import cn.genn.ai.hub.app.application.dto.AgentCompleteRespDTO;
import cn.genn.ai.hub.app.application.dto.InitAppChatDTO;
import cn.genn.ai.hub.app.application.dto.request.AgentRequest;
import cn.genn.ai.hub.app.application.dto.response.AgentResponse;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.AgentAppConstants;
import cn.genn.ai.hub.app.infrastructure.exception.AgentInvokeErrorCode;
import cn.genn.ai.hub.app.infrastructure.exception.AgentInvokeException;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentChannelRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @description 智能体应用服务
 * @date 2025-04-09
 */
@Slf4j
@Service
public class AgentInvokeService {

    private final AgentInvokeUtils agentInvokeUtils;

    private final GennAIHubProperties gennAIHubProperties;

    private final AgentChannelRepositoryImpl agentChannelRepository;

    private final WebClient webClient;

    private final ObjectMapper objectMapper;

    public AgentInvokeService(AgentInvokeUtils agentInvokeUtils, GennAIHubProperties gennAIHubProperties,
                              AgentChannelRepositoryImpl agentChannelRepository, ObjectMapper objectMapper) {
        this.agentInvokeUtils = agentInvokeUtils;
        this.gennAIHubProperties = gennAIHubProperties;
        this.agentChannelRepository = agentChannelRepository;
        this.webClient = WebClient.builder().codecs(configurer -> configurer
            .defaultCodecs()
            .maxInMemorySize(16 * 1024 * 1024))  // 设置最大内存大小为16MB
            .build();
        this.objectMapper = objectMapper;
    }


    public String invokeAgentInitChat(String feishuAppId, String chatId) throws AgentInvokeException {
        AgentChannelPO agentChannelPO = agentChannelRepository.queryByChannelUnique(ChannelTypeEnum.FEISHU, feishuAppId);
        if (agentChannelPO == null) {
            log.error("Agent channel not found for feishu appId: {}", feishuAppId);
            return null;
        }
        String invokeUrl = gennAIHubProperties.getAgent().getInvokeDomain() + AgentAppConstants.INIT_CHAT_URL;
        String authorization = gennAIHubProperties.getAgent().getAuthorization();
        // 调用智能体初始化会话接口
        String url = invokeUrl + "?appId=" + agentChannelPO.getWorkflowId() + "&chatId=" + chatId;
        try {
            AgentResponse<InitAppChatDTO> responseResult = agentInvokeUtils.fetchGet(url, authorization,null, new TypeReference<>() {
            });
            return responseResult.getData().getApp().getChatConfig().getWelcomeText();
        } catch (Exception e) {
            log.error("Error initializing chat for workflowId: {}: {}", agentChannelPO.getWorkflowId(), e.getMessage());
            return StrUtil.EMPTY;
        }
    }

    public void clearChat(String appId, String chatId) throws AgentInvokeException {
        String invokeUrl = gennAIHubProperties.getAgent().getInvokeDomain() + AgentAppConstants.CLEAR_CHAT_URL;
        String authorization = gennAIHubProperties.getAgent().getAuthorization();
        // 调用智能体初始化会话接口
        String url = invokeUrl + "?appId=" + appId + "&chatId=" + chatId;
        try {
            AgentResponse<Void> responseResult = agentInvokeUtils.fetchDelete(url, authorization,null, new TypeReference<>() {
            });
            if (responseResult.getCode() != 200) {
                log.error("Error clearing chat for appId: {}: {}", appId, responseResult.getMessage());
            }
        } catch (Exception e) {
            log.error("Error clearing chat for appId {}: {}", appId, e.getMessage());
            throw new AgentInvokeException(AgentInvokeErrorCode.CLEAR_CHAT, "Error clearing chat for appId: " + appId, e);
        }
    }

    public AgentCompleteRespDTO invokeStreamGetAnswer(String appId, String chatId, String channelUniqueIdentifier, ChannelTypeEnum channelType, String openId, String userMessage)
        throws AgentInvokeException {
        AgentRequest request = buildAgentRequest(appId, chatId, channelUniqueIdentifier, channelType, Map.of(AgentAppConstants.CHANNEL_USER_ID, openId), userMessage);
        try {
            log.info("invoke stream get answer request body: {}", JsonUtils.toJson(request));
            AgentCompleteRespDTO completeResponse = getCompleteResponse(
                gennAIHubProperties.getAgent().getInvokeDomain() + AgentAppConstants.INVOKE_APP_URL,
                JsonUtils.toJson(request),
                gennAIHubProperties.getAgent().getAuthorization()
            );
            log.info("invoke stream get answer response: {}", completeResponse);
            return completeResponse;
        } catch (Exception e) {
            log.error("Error getting agent answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    private AgentCompleteRespDTO getCompleteResponse(String apiUrl, String body, String authorization) throws AgentInvokeException {
        try {
            CompletableFuture<AgentCompleteRespDTO> resultFuture = new CompletableFuture<>();
            StringBuilder answerBuilder = new StringBuilder();
            AtomicReference<JsonNode> lastInteractiveDataNode = new AtomicReference<>(null);
            webClient.post()
                .uri(apiUrl)
                .headers(h -> h.addAll(agentInvokeUtils.fetchJsonHeader(authorization)))
                .bodyValue(body)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                })
                .doOnNext(sse -> {
                    log.info("SSE Received: event='{}', data='{}'", sse.event(), sse.data());
                    String eventType = sse.event();
                    String eventData = sse.data();

                    if ("[DONE]".equals(eventData)) {
                        log.info("SSE stream [DONE] marker received.");
                        return; // 在 doOnComplete 中处理
                    }

                    try {
                        if (eventType != null) {
                            switch (eventType) {
                                case "answer", "fastAnswer" -> processAnswer(eventType, eventData, answerBuilder);
                                case "interactive" -> {
                                    lastInteractiveDataNode.set(objectMapper.readTree(eventData));
                                    log.info("Stored interactive event data.");
                                }
                                case "flowResponses" -> log.debug("Received flowResponses event, data: {}", eventData);
                                // 当前 DTO 不需要，忽略
                                default ->
                                    log.warn("Unsupported or unhandled SSE event type: {} or data format. Data: {}", eventType, eventData);
                            }
                        }
                    } catch (JsonProcessingException e) {
                        log.error("Failed to parse SSE data as JsonNode. Event: {}, Data: {}, Error: {}", eventType, eventData, e.getMessage(), e);
                    }
                })
                .doOnComplete(() -> {
                    log.info("SSE stream completed.");
                    AgentCompleteRespDTO finalDto = new AgentCompleteRespDTO();
                    finalDto.setAnswer(answerBuilder.toString());
                    JsonNode interactiveData = lastInteractiveDataNode.get();
                    fillInteractive(interactiveData, finalDto);
                    resultFuture.complete(finalDto);
                })
                .doOnError(throwable -> {
                    log.error("获取流式响应失败, url: {}, body: {}", apiUrl, body, throwable);
                    resultFuture.completeExceptionally(throwable);
                })
                .subscribe(); // 记得 subscribe 启动流式请求

            return resultFuture.get();
        } catch (Exception e) {
            log.error("获取流式响应失败, url: {}, body: {}", apiUrl, body, e);
            throw new AgentInvokeException(AgentInvokeErrorCode.COMPLETE, e.getMessage());
        }
    }

    private void fillInteractive(JsonNode interactiveData, AgentCompleteRespDTO finalDto) {
        if (interactiveData != null && interactiveData.has("interactive")) {
            JsonNode interactiveNode = interactiveData.path("interactive");
            AgentCompleteRespDTO.InteractiveContent dtoInteractive = new AgentCompleteRespDTO.InteractiveContent();
            dtoInteractive.setType(interactiveNode.path("type").asText(null));

            if (interactiveNode.has("params")) {
                JsonNode paramsNode = interactiveNode.path("params");
                AgentCompleteRespDTO.Params dtoParams = new AgentCompleteRespDTO.Params();
                dtoParams.setDescription(paramsNode.path("description").asText(null));

                // Populate inputForm if present
                if (paramsNode.has("inputForm") && paramsNode.path("inputForm").isArray()) {
                    List<AgentCompleteRespDTO.InputFormItem> inputFormItems = new ArrayList<>();
                    for (JsonNode itemNode : paramsNode.path("inputForm")) {
                        AgentCompleteRespDTO.InputFormItem item = new AgentCompleteRespDTO.InputFormItem();
                        item.setType(itemNode.path("type").asText(null));
                        item.setKey(itemNode.path("key").asText(null));
                        item.setLabel(itemNode.path("label").asText(null));
                        item.setDescription(itemNode.path("description").asText(null));
                        item.setValue(itemNode.path("value").asText(null));
                        item.setDefaultValue(itemNode.path("defaultValue").asText(null));
                        item.setValueType(itemNode.path("valueType").asText(null));
                        item.setRequired(itemNode.path("required").asBoolean(false));
                        Optional.ofNullable(itemNode.get("maxLength"))
                            .ifPresent(maxLengthNode -> item.setMaxLength(maxLengthNode.asInt()));
                        // 正确处理 list 字段为对象类型
                        if (itemNode.has("list") && itemNode.path("list").isArray()) {
                            List<AgentCompleteRespDTO.InputSelectOption> listValues = new ArrayList<>();
                            for (JsonNode listItem : itemNode.path("list")) {
                                AgentCompleteRespDTO.InputSelectOption option = new AgentCompleteRespDTO.InputSelectOption();
                                option.setLabel(listItem.path("label").asText(null));
                                option.setValue(listItem.path("value").asText(null));
                                listValues.add(option);
                            }
                            item.setList(listValues);
                        } else {
                            item.setList(null);
                        }
                        inputFormItems.add(item);
                    }
                    dtoParams.setInputForm(inputFormItems);
                }

                // Populate userSelectOptions if present
                if (paramsNode.has("userSelectOptions") && paramsNode.path("userSelectOptions").isArray()) {
                    List<AgentCompleteRespDTO.UserSelectOption> selectOptions = new ArrayList<>();
                    for (JsonNode optionNode : paramsNode.path("userSelectOptions")) {
                        AgentCompleteRespDTO.UserSelectOption option = new AgentCompleteRespDTO.UserSelectOption();
                        option.setKey(optionNode.path("key").asText(null));
                        option.setValue(optionNode.path("value").asText(null));
                        selectOptions.add(option);
                    }
                    dtoParams.setUserSelectOptions(selectOptions);
                }
                dtoInteractive.setParams(dtoParams);
            }
            finalDto.setInteractive(dtoInteractive);
        }
    }

    private void processAnswer(String eventType, String data, StringBuilder builder) {
        JsonNode answer;
        try {
            answer = objectMapper.readTree(data);
        } catch (JsonProcessingException e) {
            log.error("解析 JSON 失败, data: {}", data, e);
            return;
        }
        JsonNode choices = answer.path("choices");

        for (JsonNode choice : choices) {
            JsonNode delta = choice.path("delta");
            String content = delta.path("content").asText(null);

            if (content != null) {
                if ("fastAnswer".equals(eventType) && content.startsWith("\n")) {
                    // 去掉开头的 "\n" (两个字符) （特殊处理）
                    content = content.substring(1);
                }
                builder.append(content);
            }
        }
    }

    private AgentRequest buildAgentRequest(String appId, String chatId, String channelUniqueIdentifier, ChannelTypeEnum channelType, Map<String, Object> variables, String userMessage) {
        AgentRequest.MessageDTO messageDTO = AgentRequest.MessageDTO.builder()
            .content(userMessage)
            .role("user")
            .hideInUI(false)
            .build();

        return AgentRequest.builder()
            .appId(appId)
            .chatId(chatId)
            .platform(channelType.getCode())
            .channelUniqueIdentifier(channelUniqueIdentifier)
            .variables(variables)
            .detail(true)
            .stream(true)
            .messages(List.of(messageDTO))
            .build();
    }

}
