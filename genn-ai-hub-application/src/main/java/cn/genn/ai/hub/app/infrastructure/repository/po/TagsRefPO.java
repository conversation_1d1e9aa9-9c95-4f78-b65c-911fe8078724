package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * TagsRefPO对象
 *
 * <AUTHOR>
 * @desc 标签关联表
 */
@Data
@TableName(value = "tags_ref", autoResultMap = true)
public class TagsRefPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 标签id
     */
    @TableField("tag_id")
    private Long tagId;

    /**
     * 资源key
     */
    @TableField("resource_key")
    private String resourceKey;

}

