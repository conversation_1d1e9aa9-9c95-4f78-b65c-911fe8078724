package cn.genn.ai.hub.app.infrastructure.external.feishu.event;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 飞书事件基类
 * @date 2025-04-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeishuBaseEvent {

    // 公共字段（v1.0和v2.0共有）
    protected String appId;
    protected String tenantKey;
    protected String challenge;
    protected String eventType; // 统一抽象事件类型
    protected JsonNode event; // 动态 event 结构
    protected String timestamp;
    protected String signature;
    protected String nonce;

    public String getEventId() {
        return event != null ? event.path("event_id").asText() : null;
    }

    public String getToken() {
        return event != null ? event.path("token").asText() : null;
    }
}
