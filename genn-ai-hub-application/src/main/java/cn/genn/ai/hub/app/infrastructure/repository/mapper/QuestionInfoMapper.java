package cn.genn.ai.hub.app.infrastructure.repository.mapper;

import cn.genn.ai.hub.app.application.dto.question.*;
import cn.genn.ai.hub.app.application.query.AgentFeedbackAnalysisQuery;
import cn.genn.ai.hub.app.application.query.DepartmentQuestionCountQuery;
import cn.genn.ai.hub.app.application.query.UserQuestionCountQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionInfoPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QuestionInfoMapper extends BaseMapper<QuestionInfoPO> {

    List<HighFreAnalysisInfo> getListByAppIdAndSource(@Param("appId") String appId, @Param("source") String source);

    List<QuestionInfoDTO> getAllAnalysisInfo(@Param("appId") String appId);

    /**
     * 获取用户问题统计
     */
    List<UserQuestionCountDTO> getTopUsers(@Param("query") UserQuestionCountQuery query);

    /**
     * 获取部门问题统计
     */
    @IgnoreTenant
    List<DepartmentQuestionCountDTO> getDepartmentTop10(@Param("query") DepartmentQuestionCountQuery query);

    /**
     * 获取部门人员统计
     */
    @IgnoreTenant
    List<DepartmentUserDTO> getDepartmentUsers(@Param("query") DepartmentQuestionCountQuery query, @Param("departments") List<String> departments);

    /**
     * 获取部门历史问题数量统计
     */
    @IgnoreTenant
    List<DepartmentQuestionCountDTO> getDepartmentHistoryCount(@Param("query") DepartmentQuestionCountQuery query);

    /**
     * 获取用户历史问题数量统计
     */
    @IgnoreTenant
    Long getUserHistoryCount(@Param("query") AgentFeedbackAnalysisQuery query);

    default void updateTypeByIds(@Param("ids") List<Long> ids, @Param("type") String type){
        this.update(new QuestionInfoPO(), new UpdateWrapper<QuestionInfoPO>()
                .lambda()
                .set(QuestionInfoPO::getType, type)
                .in(QuestionInfoPO::getId, ids));
    }
}
