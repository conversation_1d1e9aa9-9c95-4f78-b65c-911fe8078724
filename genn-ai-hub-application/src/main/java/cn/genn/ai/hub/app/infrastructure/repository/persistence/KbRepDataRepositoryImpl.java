package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoDataAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoDataDTO;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoDataMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataPO;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Collection;
import java.util.List;

/**
 * @Date: 2025/3/7
 * @Author: kangjian
 */
@Repository
public class KbRepDataRepositoryImpl extends ServiceImpl<KbRepoDataMapper, KbRepoDataPO> {

    @Resource
    private KbRepoDataMapper dataMapper;
    @Resource
    private KbRepoDataAssembler dataAssembler;
    @Resource
    private KbRepCollectionRepositoryImpl collectionRepository;

    public void deleteDataByDataKey(String dataKey) {

        QueryWrapper<KbRepoDataPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataPO::getDataKey, dataKey);
        KbRepoDataPO repoDataPO = dataMapper.selectOne(queryWrapper);
        dataMapper.delete(queryWrapper);
        asyncCollDataSize(repoDataPO.getRepoId(), repoDataPO.getCollectionId());
    }

    private void asyncCollDataSize(Long repoId, Long collId) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                Thread.ofVirtual()
                    .name("thread_async_calculate_dataSize_" + collId)
                    .start(() -> {
                        Long dataSize = SpringUtil.getBean(KbRepDataRepositoryImpl.class).countByRepoIdAndCollectionId(repoId, collId);
                        collectionRepository.updateDataSizeById(repoId, collId, dataSize);
                    });
            }
        });
    }

    public void updateDataAndHandleStatus(KbRepoDataPO po) {
        po.setHandleStatus(HandleStatusEnum.WAIT);
        po.setRetryCount(0);
        UpdateWrapper<KbRepoDataPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoDataPO::getDataKey, po.getDataKey())
            .eq(KbRepoDataPO::getRepoId, po.getRepoId())
            .eq(KbRepoDataPO::getCollectionId, po.getCollectionId());
        dataMapper.update(po, updateWrapper);
    }

    public List<KbRepoDataPO> selectRepoDataListByCollectionId(Long collectionId) {
        QueryWrapper<KbRepoDataPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataPO::getCollectionId, collectionId)
            .eq(KbRepoDataPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return dataMapper.selectList(queryWrapper);
    }

    public Long saveKbRepoData(KbRepoDataPO kbRepoDataPO) {
        // 如果没有分配data_key 生成一个
        if (kbRepoDataPO.getDataKey() == null) {
            kbRepoDataPO.setDataKey(UUID.randomUUID().toString());
        }
        dataMapper.insert(kbRepoDataPO);
        asyncCollDataSize(kbRepoDataPO.getRepoId(), kbRepoDataPO.getCollectionId());
        return kbRepoDataPO.getId();
    }

    /**
     * 算法的回调批量新增文本分块后的数据
     *
     * @param kbRepoDataPO
     * @return
     */
    public List<BatchResult> batchSaveKbRepoData(List<KbRepoDataPO> kbRepoDataPO) {
        if (CollUtil.isEmpty(kbRepoDataPO)) {
            return Lists.newArrayList();
        }
        List<BatchResult> result = dataMapper.insert(kbRepoDataPO, 10);
        asyncCollDataSize(kbRepoDataPO.getFirst().getRepoId(), kbRepoDataPO.getFirst().getCollectionId());
        return result;
    }

    @IgnoreTenant
    public Long countByRepoIdAndCollectionId(Long repoId, Long collectionId) {
        QueryWrapper<KbRepoDataPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataPO::getRepoId, repoId)
            .eq(KbRepoDataPO::getCollectionId, collectionId)
            .eq(KbRepoDataPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return dataMapper.selectCount(queryWrapper);
    }

    @IgnoreTenant
    public KbRepoDataPO queryByDataKey(String dataKey) {
        QueryWrapper<KbRepoDataPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataPO::getDataKey, dataKey)
            .eq(KbRepoDataPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return dataMapper.selectOne(queryWrapper);
    }

    @IgnoreTenant
    public List<KbRepoDataDTO> selectDateByKeys(Collection<String> dateKeys) {
        QueryWrapper<KbRepoDataPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .in(KbRepoDataPO::getDataKey, dateKeys)
            .eq(KbRepoDataPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return dataAssembler.PO2DTO(dataMapper.selectList(queryWrapper));
    }

    public void removeByCollId(Long repoId, Long collId) {
        QueryWrapper<KbRepoDataPO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .lambda()
            .eq(KbRepoDataPO::getRepoId, repoId)
            .eq(KbRepoDataPO::getCollectionId, collId)
        ;
        dataMapper.delete(queryWrapper);
    }

    public void updateDataExtData(Long id, String json) {
        UpdateWrapper<KbRepoDataPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoDataPO::getId, id)
            .set(KbRepoDataPO::getExtData, json);
        dataMapper.update(null, updateWrapper);
    }

    public void updateDataContent(String dataKey, String question, String answer) {
        UpdateWrapper<KbRepoDataPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoDataPO::getDataKey, dataKey)
            .set(StrUtil.isNotBlank(question), KbRepoDataPO::getQuestion, question)
            .set(StrUtil.isNotBlank(answer), KbRepoDataPO::getAnswer, answer);
        dataMapper.update(null, updateWrapper);
    }
}
