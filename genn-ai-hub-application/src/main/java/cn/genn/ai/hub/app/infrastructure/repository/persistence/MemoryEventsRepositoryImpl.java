package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.MemoryEventsAssembler;
import cn.genn.ai.hub.app.application.command.MemoryEventsCommand;
import cn.genn.ai.hub.app.application.dto.MemoryEventsDTO;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.query.MemoryEventsQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.MemoryEventsMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.MemoryEventsPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 情景记忆事件仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class MemoryEventsRepositoryImpl extends ServiceImpl<MemoryEventsMapper, MemoryEventsPO> {

    private final MemoryEventsMapper mapper;
    private final MemoryEventsAssembler assembler;

    public PageResultDTO<MemoryEventsDTO> page(MemoryEventsQuery query) {
        MemoryEventsPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    @IgnoreTenant
    public MemoryEventsDTO selectById(Long id) {
        return assembler.PO2DTO(getById(id));
    }

    public Long create(MemoryEventsCommand command) {
        MemoryEventsPO po = assembler.convertPO(command);
        mapper.insert(po);
        return po.getId();
    }

    public List<MemoryEventsDTO> batchCreate(List<MemoryEventsCommand> commands) {
        List<MemoryEventsPO> pos = assembler.convertPOs(commands);
        saveBatch(pos);
        return assembler.PO2DTO(pos);
    }

    public void edit(MemoryEventsCommand command) {
        MemoryEventsPO po = assembler.convertPO(command);
        mapper.updateById(po);
    }

    public void deleteById(Long id) {
        batchDelete(Lists.newArrayList(id));
    }

    public void batchDelete(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        UpdateWrapper<MemoryEventsPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .in(MemoryEventsPO::getId, ids);
        mapper.update(null, updateWrapper);
    }

    @IgnoreTenant
    public void updateMemoryEventsHandleStatus(Long memoryEventsId, HandleStatusEnum status) {
        UpdateWrapper<MemoryEventsPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(MemoryEventsPO::getId, memoryEventsId)
            .set(MemoryEventsPO::getHandleStatus, status);
        mapper.update(null, updateWrapper);
    }

}
