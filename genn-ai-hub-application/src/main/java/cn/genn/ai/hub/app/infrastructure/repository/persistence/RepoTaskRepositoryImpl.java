package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoTaskAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoTaskMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTaskPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 运单仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class RepoTaskRepositoryImpl extends ServiceImpl<KbRepoTaskMapper, KbRepoTaskPO> {

    @Resource
    private KbRepoTaskMapper taskMapper;
    @Resource
    private KbRepoTaskAssembler taskAssembler;


    @IgnoreTenant
    public KbRepoTaskDTO getInfo(Long id) {
        return taskAssembler.convert(taskMapper.selectById(id));
    }

    @IgnoreTenant
    public Boolean updateTaskStatus(Long id, TaskStatusEnum status) {
        UpdateWrapper<KbRepoTaskPO> update = new UpdateWrapper<>();
        update.lambda()
            .in(KbRepoTaskPO::getId, id)
            .set(KbRepoTaskPO::getTaskStatus, status)
        ;
        return update(update);
    }

    @IgnoreTenant
    public Boolean updateTaskStatusOfFail(Long id, String remark) {
        UpdateWrapper<KbRepoTaskPO> update = new UpdateWrapper<>();
        update.lambda()
            .in(KbRepoTaskPO::getId, id)
            .set(KbRepoTaskPO::getTaskStatus, TaskStatusEnum.FAILED)
            .set(KbRepoTaskPO::getRemark, remark)
        ;
        return update(update);
    }

    public List<KbRepoTaskDTO> findTaskByRepoId(Long repoId) {
        QueryWrapper<KbRepoTaskPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoTaskPO::getRepoId, repoId)
//            .in(KbRepoTaskPO::getTaskStatus, Lists.newArrayList(TaskStatusEnum.PENDING, TaskStatusEnum.PROCESSING))
            .eq(KbRepoTaskPO::getDeleted, DeletedEnum.NOT_DELETED)
        ;
        return taskAssembler.convert(taskMapper.selectList(queryWrapper));
    }

    public List<KbRepoTaskDTO> getInfoByIds(Collection<Long> taskIds) {
        return taskAssembler.convert(taskMapper.selectByIds(taskIds));
    }

    public List<KbRepoTaskDTO> findProcessingTasks() {
        QueryWrapper<KbRepoTaskPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoTaskPO::getTaskStatus, TaskStatusEnum.PROCESSING)
            .eq(KbRepoTaskPO::getDeleted, DeletedEnum.NOT_DELETED)
        ;
        return taskAssembler.convert(taskMapper.selectList(queryWrapper));

    }

    @IgnoreTenant
    public List<KbRepoTaskDTO> findPendingTasks() {
        QueryWrapper<KbRepoTaskPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoTaskPO::getTaskStatus, TaskStatusEnum.PENDING)
            .eq(KbRepoTaskPO::getDeleted, DeletedEnum.NOT_DELETED)
        ;
        return taskAssembler.convert(taskMapper.selectList(queryWrapper));

    }
}
