package cn.genn.ai.hub.app.infrastructure.external.feishu.utils;

import cn.genn.ai.hub.app.application.dto.AgentCompleteRespDTO;
import cn.genn.ai.hub.app.application.dto.feishu.EventHandleDTO;
import cn.genn.ai.hub.app.application.enums.feishu.ChatTypeEnum;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.infrastructure.exception.AgentInvokeException;
import cn.genn.ai.hub.app.infrastructure.external.agent.AgentInvokeService;
import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.Client;
import com.lark.oapi.service.im.v1.enums.MsgTypeEnum;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.lark.oapi.service.im.v1.model.ReplyMessageReq;
import com.lark.oapi.service.im.v1.model.ReplyMessageReqBody;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description 飞书消息发送器
 * @date 2025-05-13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FeishuMessageSender {

    private final Executor virtualThreadExecutor;

    private final AgentInvokeService agentInvokeService;

    private final FeishuClientService feishuClientService;

    // 统一消息发送入口
    public void handleTextMessage(EventHandleDTO command, ChatTypeEnum chatType, String appId,
                                  String userMessage, String messageId, String chatId, String openId) {
        virtualThreadExecutor.execute(() -> processMessage(command, appId, chatId, openId, userMessage, messageId, chatType));
    }

    private void processMessage(EventHandleDTO command, String appId, String chatId, String openId, String userMessage, String messageId, ChatTypeEnum chatType) {
        try {
            String agentChatId = FeishuMessageUtils.generateChatId(chatType, chatId, openId);
            if ("/clear".equalsIgnoreCase(userMessage)) {
                Client client = feishuClientService.getClient(command.getAppId());
                // 清理会话
                try {
                    agentInvokeService.clearChat(appId, agentChatId);
                } catch (AgentInvokeException ex) {
                    log.error("Failed to clear chat | appId:{} | chatId:{} | errorMsg:{}", appId, agentChatId, ex.getMessage());
                    // 发送清理失败消息
                    sendMessage(client, chatId,
                        Map.of("text", "Failed to clear context"),
                        MsgTypeEnum.MSG_TYPE_TEXT, ChatTypeEnum.GROUP.equals(chatType), messageId);
                    return;
                }
                // 发送已清理消息
                boolean isGroup = ChatTypeEnum.GROUP.equals(chatType);
                sendMessage(client, chatId,
                    Map.of("text", "Context cleared"),
                    MsgTypeEnum.MSG_TYPE_TEXT, isGroup, messageId);
                return;
            }
            AgentCompleteRespDTO response = agentInvokeService.invokeStreamGetAnswer(
                appId,
                agentChatId,
                command.getAppId(),
                ChannelTypeEnum.FEISHU,
                openId,
                userMessage
            );

            if (response != null) {
                sendCompositeMessage(command, response, appId, messageId, chatId, openId, chatType);
            }
        } catch (Exception e) {
            log.error("Agent invocation failed | appId:{} | chatId:{} | errorMsg:{}",
                appId, chatId, e.getMessage(), e);
        }
    }

    private void sendCompositeMessage(EventHandleDTO command, AgentCompleteRespDTO response, String appId, String messageId, String chatId, String openId, ChatTypeEnum chatType) {
        Client client = feishuClientService.getClient(command.getAppId());
        boolean isGroup = ChatTypeEnum.GROUP.equals(chatType);

        // 发送文本消息
        if (StrUtil.isNotEmpty(response.getAnswer())) {
            sendMessage(client, chatId,
                Map.of("text", response.getAnswer()),
                MsgTypeEnum.MSG_TYPE_TEXT, isGroup, messageId);
        }

        // 发送交互卡片
        Optional.ofNullable(response.getInteractive())
            .map(interactive -> FeishuCardUtils.convert2Card(
                interactive, appId,
                chatId, messageId,
                openId, chatType))
            .ifPresent(card -> sendMessage(client, chatId,
                card, MsgTypeEnum.MSG_TYPE_INTERACTIVE, isGroup, messageId));
    }

    // 统一消息发送方法
    private void sendMessage(Client client, String targetId, Object content,
                             MsgTypeEnum msgType, boolean isGroup, String messageId) {
        try {
            if (isGroup) {
                replyGroupMessage(client, messageId, content, msgType);
            } else {
                sendP2PMessage(client, targetId, content, msgType);
            }
        } catch (Exception e) {
            log.error("Message send failed | errorMsg: {}", e.getMessage(), e);
        }
    }

    // 群聊消息回复
    private void replyGroupMessage(Client client, String messageId, Object content, MsgTypeEnum msgType) {
        ReplyMessageReq request = ReplyMessageReq.newBuilder()
            .messageId(messageId)
            .replyMessageReqBody(ReplyMessageReqBody.newBuilder()
                .content(JsonUtils.toJson(content))
                .msgType(msgType.getValue())
                .build())
            .build();

        try {
            client.im().v1().message().reply(request);
        } catch (Exception e) {
            log.error("Group reply failed | messageId:{}", messageId, e);
        }
    }

    // 私聊消息发送
    private void sendP2PMessage(Client client, String chatId, Object content, MsgTypeEnum msgType) {
        CreateMessageReq request = FeishuMessageUtils.buildMessageRequest(chatId, content, msgType);
        try {
            CreateMessageResp response = client.im().message().create(request);
            FeishuMessageUtils.logMessageResponse(chatId, response);
        } catch (Exception e) {
            log.error("P2P message failed | chatId:{}", chatId, e);
        }
    }
}
