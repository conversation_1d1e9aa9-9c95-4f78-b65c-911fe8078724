
package cn.genn.ai.hub.app.infrastructure.repository.config;

import cn.genn.ai.hub.app.application.dto.ModelManageDTO;
import cn.genn.ai.hub.app.application.enums.ModelTypeEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.json.JSONUtil;

/**
 * 模型不同的配置，通过子类来定义
 *
 * <AUTHOR>
 */
public class ModelConfigUtils {


    public static ModelConfig parseConfig(ModelTypeEnum type, String configStr) {
        // 解析配置
        Class<? extends ModelConfig> configClass = type.getConfigClass();
        ModelConfig config = JSONUtil.toBean(configStr, configClass);
        if (config == null || !config.validate()) {
            return null;
        }
        return config;
    }

    public static final String json = "{\n" +
        "    \"model\": \"gpt-4o-mini7\",\n" +
        "    \"providerId\": 1,\n" +
        "    \"providerName\": \"OpenAI\",\n" +
        "    \"name\": \"gpt-4o-mini7\",\n" +
        "    \"type\": 1,\n" +
        "    \"apiKey\": \"dsadsjadhsajkdhksa\",\n" +
        "    \"baseUrl\": \"https://api.openai.com\",\n" +
        "    \"apiPath\": \"/v1\",\n" +
        "    \"isActive\": true,\n" +
        "    \"commonConfig\": \"{\\\"maxContext\\\":128000,\\\"maxResponse\\\":4000,\\\"quoteMaxToken\\\":120000,\\\"maxTemperature\\\":0.99,\\\"showTopP\\\":true,\\\"responseFormatList\\\":[\\\"text\\\",\\\"json_object\\\"],\\\"showStopSign\\\":true,\\\"vision\\\":true,\\\"toolChoice\\\":true,\\\"functionCall\\\":true,\\\"defaultSystemChatPrompt\\\":\\\"\\\",\\\"datasetProcess\\\":true,\\\"usedInClassify\\\":true,\\\"customCQPrompt\\\":\\\"\\\",\\\"usedInExtractFields\\\":true,\\\"usedInQueryExtension\\\":true,\\\"customExtractPrompt\\\":\\\"\\\",\\\"usedInToolCall\\\":true,\\\"fieldMap\\\":{},\\\"isCustom\\\":true,\\\"charsPointsPrice\\\":0,\\\"reasoning\\\":true}\",\n" +
        "    \"paramConfig\": \"{}\"\n" +
        "}";

    public static void main(String[] args) {
        ModelManageDTO create = JsonUtils.parse(json, ModelManageDTO.class);

        ModelConfig modelConfig = ModelConfigUtils.parseConfig(create.getType(), create.getCommonConfig());
        System.out.println();
    }
}
