package cn.genn.ai.hub.app.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionGapAnalysisPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QuestionGapAnalysisMapper extends BaseMapper<QuestionGapAnalysisPO> {

    List<String> getAllTypes(@Param("appId") String appId);
}
