package cn.genn.ai.hub.app.infrastructure.config;

import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class TaskQueueProperties {

    /**
     * 请求接口路径
     */
    private Map<TaskTypeEnum, String> requestPath;
    /**
     * 进行队列
     */
    private Map<TaskTypeEnum, Long> process;

    /**
     * 任务队列
     */
    private Map<TaskTypeEnum, Long> task;

    private Long retryIntervalTime;

    public Long getProcessLimit(TaskTypeEnum type) {
        return process.get(type) != null ? process.get(type) : 1000L;
    }

    public Long getTaskLimit(TaskTypeEnum type) {
        return task.get(type) != null ? task.get(type) : 50L;
    }
}
