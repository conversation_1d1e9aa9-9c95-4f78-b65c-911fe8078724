package cn.genn.ai.hub.app.infrastructure.external.agent;

import cn.genn.ai.hub.app.application.dto.response.AgentResponse;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 智能体调用服务
 * @date 2025-04-09
 */
@Slf4j
@Component
public class AgentInvokeUtils {

    private final RestTemplate restTemplate;

    public AgentInvokeUtils(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     *通用的 HTTP 请求方法
     *
     * @param url           请求的完整 URL
     * @param method        HTTP 方法 (GET, POST, DELETE, etc.)
     * @param authorization Authorization 头信息 (可选)
     * @param requestBody   请求体 (对于 POST/PUT 等方法, 可选)
     * @param typeRef       期望的响应体类型引用，用于反序列化 AgentResponse<T>
     * @return AgentResponse<T> 包含处理结果
     * @throws BusinessException 如果业务逻辑失败 (如 API 返回错误码)
     * @throws Exception         如果发生其他网络或序列化异常
     */
    public <T> AgentResponse<T> fetch(String url, HttpMethod method, String authorization, Object requestBody,
                                      TypeReference<AgentResponse<T>> typeRef) throws Exception {
        try {
            log.info("Invoking {} on URL: {}", method, url);
            if (requestBody != null) {
                log.debug("Request body: {}", JsonUtils.toJson(requestBody)); // 打印请求体（注意敏感信息）
            }

            HttpHeaders headers = fetchJsonHeader(authorization);
            HttpEntity<?> httpEntity;

            if (requestBody != null) {
                httpEntity = new HttpEntity<>(JsonUtils.toJson(requestBody), headers);
            } else {
                httpEntity = new HttpEntity<>(headers);
            }

            ResponseEntity<String> responseEntity = restTemplate.exchange(
                url,
                method,
                httpEntity,
                String.class
            );

            log.info("Invocation to {} completed. Status: {}, Response body: {}", url, responseEntity.getStatusCode(), responseEntity.getBody());
            return handleResponse(responseEntity, typeRef);

        } catch (HttpStatusCodeException e) {
            log.error("HTTP error invoking {} on URL: {}. Status: {}, Response: {}", method, url, e.getStatusCode(), e.getResponseBodyAsString(), e);
            // 可以根据 e.getStatusCode() 和 e.getResponseBodyAsString() 抛出更具体的 BusinessException
            throw new BusinessException(MessageCode.HTTP_ERROR, "远程调用失败，状态码: " + e.getStatusCode() + ", 响应: " + e.getResponseBodyAsString());
        } catch (BusinessException e) { // Re-throw BusinessExceptions from handleResponse
            throw e;
        } catch (Exception e) {
            log.error("Error invoking {} on URL: {}. Error: {}", method, url, e.getMessage(), e);
            throw new Exception(method + "请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 辅助方法：发送 GET 请求
     */
    public <T> AgentResponse<T> fetchGet(String baseUrl, String authorization, Map<String, Object> queryParams,
                                         TypeReference<AgentResponse<T>> typeRef) throws Exception {
        String finalUrl = buildUrlWithParams(baseUrl, queryParams);
        return fetch(finalUrl, HttpMethod.GET, authorization, null, typeRef);
    }

    /**
     * 辅助方法：发送 DELETE 请求
     */
    public <T> AgentResponse<T> fetchDelete(String baseUrl, String authorization, Map<String, Object> queryParams,
                                            TypeReference<AgentResponse<T>> typeRef) throws Exception {
        String finalUrl = buildUrlWithParams(baseUrl, queryParams);
        return fetch(finalUrl, HttpMethod.DELETE, authorization, null, typeRef);
    }

    /**
     * 发送 POST 请求
     */
    public <T> AgentResponse<T> fetchPost(String url, String authorization, Object requestBody,
                                          TypeReference<AgentResponse<T>> typeRef) throws Exception {
        // POST 请求通常不通过 queryParams 构建 URL，而是直接使用传入的 url
        return fetch(url, HttpMethod.POST, authorization, requestBody, typeRef);
    }


    /**
     * 构建带查询参数的 URL
     * @param baseUrl     基础 URL
     * @param queryParams 查询参数 Map
     * @return 拼接了查询参数的完整 URL 字符串
     */
    private String buildUrlWithParams(String baseUrl, Map<String, Object> queryParams) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(baseUrl); // 使用 fromUriString 更灵活
        if (queryParams != null && !queryParams.isEmpty()) {
            queryParams.forEach((key, value) -> {
                if (value != null) { // 避免 null 值参数
                    builder.queryParam(key, value);
                }
            });
        }
        return builder.build().toUriString();
    }

    /**
     * 处理 HTTP 响应，将其转换为 AgentResponse<T>
     * @param responseEntity ResponseEntity 包含原始响应字符串
     * @param typeRef        类型引用，用于反序列化
     * @return AgentResponse<T>
     * @throws BusinessException 如果响应状态不是 OK，或者反序列化结果指示业务失败
     */
    private <T> AgentResponse<T> handleResponse(ResponseEntity<String> responseEntity, TypeReference<AgentResponse<T>> typeRef) {
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("HTTP request failed with status code: {} and body: {}", responseEntity.getStatusCode(), responseEntity.getBody());
            throw new BusinessException(MessageCode.HTTP_ERROR, "远程调用失败，状态码: " + responseEntity.getStatusCode());
        }

        String responseBody = responseEntity.getBody();
        if (responseBody.isEmpty()) {
            log.warn("Received empty response body for a 200 OK status.");
            // 根据业务需求，空响应体可能是错误，也可能对于 Void 类型的 T 是正常的
            // 如果 T 是 Void，可以考虑返回一个成功的 AgentResponse 但 data 为 null
            if (typeRef.getType().equals(new TypeReference<AgentResponse<Void>>() {}.getType())) {
                AgentResponse<T> emptySuccessResponse = new AgentResponse<>();
                emptySuccessResponse.setCode(200); // 或者从某个成功码常量获取
                emptySuccessResponse.setMessage("操作成功");
                return emptySuccessResponse;
            }
            throw new BusinessException(MessageCode.HTTP_ERROR, "响应体为空");
        }

        log.info("Raw response body for parsing: {}", responseBody);
        AgentResponse<T> responseResult = JsonUtils.parse(responseBody, typeRef);

        if (Objects.isNull(responseResult)) {
            log.error("Failed to parse response body into AgentResponse. Body: {}", responseBody);
            throw new BusinessException(MessageCode.HTTP_ERROR, "无法解析响应体");
        }

        // 假设 AgentResponse 内部有 isSuccess() 或 getCode() == 200 来判断业务成功
        if (!responseResult.isSuccess()) {
            log.warn("API call returned business error. Code: {}, Message: {}", responseResult.getCode(), responseResult.getMessage());
            throw new BusinessException(MessageCode.HTTP_ERROR, responseResult.getMessage()); // 可以考虑使用 responseResult.getCode() 构造更具体的异常
        }
        return responseResult;
    }

    /**
     * 创建包含 Content-Type 和可选 Authorization 的 HTTP 请求头
     * @param authorization Authorization token (可选)
     * @return HttpHeaders 对象
     */
    public HttpHeaders fetchJsonHeader(String authorization) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON); // 使用 MediaType 常量
        if (authorization != null && !authorization.trim().isEmpty()) {
            headers.setBearerAuth(authorization.startsWith("Bearer ") ? authorization.substring(7) : authorization);
            // 或者 headers.set("Authorization", authorization); 如果token格式不一定是Bearer
        }
        return headers;
    }
}
