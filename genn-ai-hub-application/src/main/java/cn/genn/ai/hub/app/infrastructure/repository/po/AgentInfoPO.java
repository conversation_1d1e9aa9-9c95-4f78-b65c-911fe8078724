package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.AgentTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.typehandler.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;


/**
 * AgentInfoPO对象
 *
 * <AUTHOR>
 * @desc 智能体信息
 */
@Data
@TableName(value = "agent_info", autoResultMap = true)
public class AgentInfoPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 团队id，如果为空，代表是个人空间创建
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * mongo对应的工作流id
     */
    @TableField("workflow_id")
    private String workflowId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 智能体类型，chat：CHAT-对话智能体, workflow：WORKFLOW-工作流智能体, tool_workflow：TOOL_WORKFLOW-工作流工具, tool_http：TOOL_HTTP-HTTP工具
     */
    @TableField("agent_type")
    private AgentTypeEnum agentType;

    /**
     * 额外配置
     */
    @TableField(value = "extra_config", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> extraConfig;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 版本号
     */
    @TableField("version")
    private String version;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

