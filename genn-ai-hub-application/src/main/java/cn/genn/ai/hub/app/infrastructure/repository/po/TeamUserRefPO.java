package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * TeamUserRefPO对象
 *
 * <AUTHOR>
 * @desc 团队用户关联表
 */
@Data
@TableName(value = "team_user_ref", autoResultMap = true)
public class TeamUserRefPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 团队id
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

}

