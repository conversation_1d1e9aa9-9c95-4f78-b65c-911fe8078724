package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * AuthCommonPO对象
 *
 * <AUTHOR>
 * @desc 权限-通用配置
 */
@Data
@Accessors(chain = true)
@TableName(value = "auth_common", autoResultMap = true)
public class AuthCommonPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 授权主体类型,用户,角色,团队
     */
    @TableField("subject_type")
    private SubjectType subjectType;

    /**
     * 授权主体 key，通常为 id
     */
    @TableField("subject_key")
    private String subjectKey;

    /**
     * 资源类型
     */
    @TableField("resource_type")
    private ResourceType resourceType;

    /**
     * 资源key
     */
    @TableField("resource_key")
    private String resourceKey;

    /**
     * 操作类型
     */
    @TableField("action_type")
    private ActionType actionType;

    /**
     * 授权表达式
     */
    @TableField("auth_expression")
    private String authExpression;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

}

