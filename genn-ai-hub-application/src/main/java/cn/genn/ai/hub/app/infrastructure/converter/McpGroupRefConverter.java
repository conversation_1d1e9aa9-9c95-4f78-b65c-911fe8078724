package cn.genn.ai.hub.app.infrastructure.converter;

import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroupRef;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpGroupRefPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * MCP分组关联转换器，负责领域实体与持久化对象之间的转换
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface McpGroupRefConverter {

    McpGroupRefConverter INSTANCE = Mappers.getMapper(McpGroupRefConverter.class);

    /**
     * 将领域实体转换为持久化对象
     *
     * @param mcpGroupRef 领域实体
     * @return 持久化对象
     */
    McpGroupRefPO toMcpGroupRefPO(McpGroupRef mcpGroupRef);

    /**
     * 将持久化对象转换为领域实体
     *
     * @param mcpGroupRefPO 持久化对象
     * @return 领域实体
     */
    McpGroupRef toMcpGroupRef(McpGroupRefPO mcpGroupRefPO);

    /**
     * 将领域实体列表转换为持久化对象列表
     *
     * @param mcpGroupRefs 领域实体列表
     * @return 持久化对象列表
     */
    List<McpGroupRefPO> toMcpGroupRefPOList(List<McpGroupRef> mcpGroupRefs);

    /**
     * 将持久化对象列表转换为领域实体列表
     *
     * @param mcpGroupRefPOs 持久化对象列表
     * @return 领域实体列表
     */
    List<McpGroupRef> toMcpGroupRefList(List<McpGroupRefPO> mcpGroupRefPOs);
}
