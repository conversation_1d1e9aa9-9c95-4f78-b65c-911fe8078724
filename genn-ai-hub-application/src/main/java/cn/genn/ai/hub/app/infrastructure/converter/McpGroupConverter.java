package cn.genn.ai.hub.app.infrastructure.converter;

import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroup;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpGroupPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * MCP分组转换器，负责领域实体与持久化对象之间的转换
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface McpGroupConverter {

    McpGroupConverter INSTANCE = Mappers.getMapper(McpGroupConverter.class);

    /**
     * 将领域实体转换为持久化对象
     *
     * @param mcpGroup 领域实体
     * @return 持久化对象
     */
    McpGroupPO toMcpGroupPO(McpGroup mcpGroup);

    /**
     * 将持久化对象转换为领域实体
     *
     * @param mcpGroupPO 持久化对象
     * @return 领域实体
     */
    McpGroup toMcpGroup(McpGroupPO mcpGroupPO);

    /**
     * 将持久化对象列表转换为领域实体列表
     *
     * @param mcpGroupPOList 持久化对象列表
     * @return 领域实体列表
     */
    List<McpGroup> toMcpGroup(List<McpGroupPO> mcpGroupPOList);
}
