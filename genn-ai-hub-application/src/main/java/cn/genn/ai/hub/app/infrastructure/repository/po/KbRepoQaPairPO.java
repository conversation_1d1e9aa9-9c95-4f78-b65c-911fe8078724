package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;


/**
 * KbRepoQaPairPO对象
 *
 * <AUTHOR>
 * @desc 知识库问答对
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName(value = "kb_repo_qa_pair", autoResultMap = true)
public class KbRepoQaPairPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 知识库ID，标识集合所属的数据库（关联 repo 表）
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 问答对唯一标识
     */
    @TableField("qa_pair_key")
    private String qaPairKey;

    /**
     * 问题
     */
    @TableField("question")
    private String question;

    /**
     * 相似问题
     */
    @TableField(value = "similar_questions", typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<String> similarQuestions;

    /**
     * 回答
     */
    @TableField("answer")
    private String answer;

    /**
     * 处理状态,0:WAIT-未处理, 1:PROCESSING-处理中,2:DONE-已处理
     */
    @TableField("handle_status")
    private HandleStatusEnum handleStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

