package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.TeamInfoAssembler;
import cn.genn.ai.hub.app.application.dto.TeamInfoDTO;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.TeamInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.TeamInfoPO;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;


/**
 * 团队信息仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class TeamInfoRepositoryImpl extends ServiceImpl<TeamInfoMapper, TeamInfoPO> {

    @Resource
    private TeamInfoMapper teamInfoMapper;

    @Resource
    private TeamInfoAssembler teamInfoAssembler;

    /**
     * 创建团队
     *
     * @param teamInfoPO 团队信息PO
     * @return 团队ID
     */
    public Long createTeam(TeamInfoPO teamInfoPO) {
        teamInfoMapper.insert(teamInfoPO);
        return teamInfoPO.getId();
    }

    /**
     * 更新团队
     *
     * @param teamInfoPO 团队信息PO
     * @return 是否更新成功
     */
    public Boolean updateTeam(TeamInfoPO teamInfoPO) {
        return updateById(teamInfoPO);
    }

    /**
     * 删除团队
     *
     * @param id 团队ID
     * @return 是否删除成功
     */
    public Boolean deleteTeam(Long id) {
        TeamInfoPO teamInfoPO = getById(id);
        if (teamInfoPO != null) {
            teamInfoPO.setDeleted(DeletedTypeEnum.DELETED);
            return updateById(teamInfoPO);
        }
        return false;
    }

    /**
     * 根据ID获取团队
     *
     * @param id 团队ID
     * @return 团队PO
     */
    public TeamInfoPO getTeamById(Long id) {
        return teamInfoMapper.selectById(id);
    }

    /**
     * 根据ID获取团队DTO
     *
     * @param id 团队ID
     * @return 团队DTO
     */
    public TeamInfoDTO getTeamDTOById(Long id) {
        return teamInfoAssembler.PO2DTO(getTeamById(id));
    }

    /**
     * 更新团队创建者
     *
     * @param teamId 团队ID
     * @param newCreatorId 新创建者ID
     * @return 是否更新成功
     */
    public Boolean updateTeamCreator(Long teamId, Long newCreatorId, String username) {
        TeamInfoPO teamInfoPO = getById(teamId);
        if (teamInfoPO == null) {
            return false;
        }
        teamInfoPO.setOwnerUserId(newCreatorId);
        teamInfoPO.setOwnerUserName(username);
        return updateById(teamInfoPO);
    }
}
