package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.PromptStatusEnum;
import cn.genn.ai.hub.app.application.enums.PromptTypeEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * PromptPO对象
 *
 * <AUTHOR>
 * @desc 提示词
 */
@Data
@Accessors(chain = true)
@TableName(value = "prompt", autoResultMap = true)
public class PromptPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 团队id，如果为空，代表是个人空间创建
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * 标题/名称
     */
    @TableField("title")
    private String title;

    /**
     * 提示词类型，1：系统提示词；2：团队空间提示词
     */
    @TableField("prompt_type")
    private PromptTypeEnum promptType;

    /**
     * 最新版本
     */
    @TableField("latest_version")
    private String latestVersion;

    /**
     * 提示词状态，1：启用 0：停用
     */
    @TableField("status")
    private PromptStatusEnum status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

}

