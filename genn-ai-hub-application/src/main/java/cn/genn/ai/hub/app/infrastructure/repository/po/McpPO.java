package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.McpTypeEnum;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.McpConfig;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.Tool;
import cn.genn.ai.hub.app.infrastructure.config.ToolJacksonTypeHandler;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.typehandler.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * McpPO对象
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "mcp", autoResultMap = true)
public class McpPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * mcp名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * mcp类型
     */
    @TableField("mcp_type")
    private McpTypeEnum mcpType;

    /**
     * mcp配置
     */
    @TableField(value = "mcp_config", typeHandler = JacksonTypeHandler.class)
    private McpConfig mcpConfig;

    /**
     * mcp工具集
     */
    @TableField(value = "tools", typeHandler = ToolJacksonTypeHandler.class)
    private List<Tool> tools;

    /**
     * 启用状态 (0: 禁用, 1: 启用)。
     */
    @TableField("enabled")
    private BooleanTypeEnum enabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

