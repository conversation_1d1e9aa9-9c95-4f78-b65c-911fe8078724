package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.domain.rtc.model.aggregates.RTCPitfallAgg;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallAnalysisInfo;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallInfo;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallItem;
import cn.genn.ai.hub.app.domain.rtc.repository.IRTCPitfallInfoRepository;
import cn.genn.ai.hub.app.infrastructure.converter.RTCPitfallConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.RTCPitfallAnalysisMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.RTCPitfallInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.RTCPitfallItemMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallAnalysisPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallItemPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Repository
public class RTCPitfallInfoRepositoryImpl extends ServiceImpl<RTCPitfallInfoMapper, RTCPitfallInfoPO> implements IRTCPitfallInfoRepository {

    @Resource
    private RTCPitfallConverter rtcPitfallConverter;

    @Resource
    private RTCPitfallItemMapper rtcPitfallItemMapper;

    @Resource
    private RTCPitfallAnalysisMapper rtcPitfallAnalysisMapper;

    @Override
    public RTCPitfallAgg selectRTCPitfallAgg(Long id) {
        RTCPitfallInfoPO rtcPitfallInfoPO = baseMapper.selectById(id);
        if (rtcPitfallInfoPO != null){
            RTCPitfallInfo rtcPitfallInfo = rtcPitfallConverter.PO2Entity(rtcPitfallInfoPO);
            List<RTCPitfallItemPO> rtcPitfallItemPOS = rtcPitfallItemMapper.selectList(Wrappers.lambdaQuery(RTCPitfallItemPO.class)
                .eq(RTCPitfallItemPO::getPitfallId, id).eq(RTCPitfallItemPO::getDeleted, DeletedEnum.NOT_DELETED));
            List<RTCPitfallItem> rtcPitfallItems = rtcPitfallConverter.toRTCPitfallItem(rtcPitfallItemPOS);
            return RTCPitfallAgg.builder()
                .rtcPitfallInfo(rtcPitfallInfo)
                .rtcPitfallItems(rtcPitfallItems)
                .build();
        }
        return null;
    }

    @Override
    public void add(RTCPitfallAgg rtcPitfallAgg) {
        RTCPitfallInfoPO rtcPitfallInfoPO = rtcPitfallConverter.entity2PO(rtcPitfallAgg.getRtcPitfallInfo());
        baseMapper.insert(rtcPitfallInfoPO);
        List<RTCPitfallItemPO> rtcPitfallItemPOs = rtcPitfallConverter.toRTCPitfallItemPO(rtcPitfallAgg.getRtcPitfallItems());
        rtcPitfallItemPOs.forEach(item -> item.setPitfallId(rtcPitfallInfoPO.getId()));
        rtcPitfallAgg.getRtcPitfallInfo().setId(rtcPitfallInfoPO.getId());
        rtcPitfallItemMapper.insert(rtcPitfallItemPOs);
    }

    @Override
    public void update(RTCPitfallAgg rtcPitfallAgg) {
        RTCPitfallInfoPO rtcPitfallInfoPO = rtcPitfallConverter.entity2PO(rtcPitfallAgg.getRtcPitfallInfo());
        rtcPitfallInfoPO.setUpdateUserName(CurrentUserHolder.getNick());
        baseMapper.updateById(rtcPitfallInfoPO);
        List<RTCPitfallItemPO> rtcPitfallItemPOs = rtcPitfallConverter.toRTCPitfallItemPO(rtcPitfallAgg.getRtcPitfallItems());
        rtcPitfallItemMapper.updateById(rtcPitfallItemPOs);
    }

    @Override
    public void addPitfallAnalysis(RTCPitfallAnalysisInfo rtcPitfallAnalysisInfo) {
        RTCPitfallAnalysisPO rtcPitfallAnalysisPO = rtcPitfallConverter.toRTCPitfallAnalysisPO(rtcPitfallAnalysisInfo);
        rtcPitfallAnalysisMapper.insert(rtcPitfallAnalysisPO);
    }

    @Override
    public void save(RTCPitfallAgg rtcPitfallAgg) {
        RTCPitfallInfoPO rtcPitfallInfoPO = rtcPitfallConverter.entity2PO(rtcPitfallAgg.getRtcPitfallInfo());
        rtcPitfallInfoPO.setUpdateUserName(CurrentUserHolder.getNick());
        baseMapper.updateById(rtcPitfallInfoPO);
        UpdateWrapper<RTCPitfallItemPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(RTCPitfallItemPO::getPitfallId, rtcPitfallInfoPO.getId()).set(RTCPitfallItemPO::getDeleted, DeletedEnum.DELETED);
        rtcPitfallItemMapper.update(updateWrapper);
        List<RTCPitfallItemPO> rtcPitfallItemPOs = rtcPitfallConverter.toRTCPitfallItemPO(rtcPitfallAgg.getRtcPitfallItems());
        rtcPitfallItemPOs.forEach(item -> item.setPitfallId(rtcPitfallInfoPO.getId()));
        rtcPitfallItemMapper.insert(rtcPitfallItemPOs);
    }
}
