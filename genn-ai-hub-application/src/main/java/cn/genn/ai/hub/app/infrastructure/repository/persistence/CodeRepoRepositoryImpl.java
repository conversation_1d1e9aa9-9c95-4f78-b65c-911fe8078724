package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.CodeRepoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.CodeRepoPO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description 代码库仓储层实现
 * @date 2025-05-16
 */
@Repository
public class CodeRepoRepositoryImpl extends ServiceImpl<CodeRepoMapper, CodeRepoPO> {
}
