package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.core.api.channel.ChannelConfig;
import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * ChannelBasePO对象
 *
 * <AUTHOR>
 * @desc 全平台渠道基础信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "channel_base", autoResultMap = true)
public class ChannelBasePO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 渠道类型（如wx_mp/feishu）
     */
    @TableField("channel_type")
    private ChannelTypeEnum channelType;

    /**
     * 全局唯一标识（如微信AppID）
     */
    @TableField("unique_identifier")
    private String uniqueIdentifier;

    /**
     * 基础静态配置
     */
    @TableField("channel_config")
    private String channelConfig;

    // 添加非持久化字段和解析方法
    @TableField(exist = false)
    private ChannelConfig parsedConfig;

    public ChannelConfig getParsedConfig() {
        if (parsedConfig == null && StrUtil.isNotBlank(channelConfig)) {
            parsedConfig = JsonUtils.parse(channelConfig, channelType.getConfigClass());
        }
        return parsedConfig;
    }

    /**
     *
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}

