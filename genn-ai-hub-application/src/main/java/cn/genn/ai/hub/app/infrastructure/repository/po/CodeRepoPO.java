package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.CodeRepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.CodeTypeEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * CodeRepoPO对象
 *
 * <AUTHOR>
 * @desc 代码库
 */
@Data
@Accessors(chain = true)
@TableName(value = "code_repo", autoResultMap = true)
public class CodeRepoPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 团队id，如果为空，代表是个人空间创建
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * 代码库类型 1:公共代码库 2:私有代码库
     */
    @TableField("code_repo_type")
    private CodeRepoTypeEnum codeRepoType;

    /**
     * 代码名称
     */
    @TableField("name")
    private String name;

    /**
     * 代码描述
     */
    @TableField("description")
    private String description;

    /**
     * 代码库类型 1:javascript 2:python
     */
    @TableField("code_type")
    private CodeTypeEnum codeType;

    /**
     * 代码内容
     */
    @TableField("content")
    private String content;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

}

