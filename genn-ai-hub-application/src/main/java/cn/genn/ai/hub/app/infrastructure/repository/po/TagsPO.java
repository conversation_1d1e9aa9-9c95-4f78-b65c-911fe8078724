package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * TagsPO对象
 *
 * <AUTHOR>
 * @desc 标签定义
 */
@Data
@TableName(value = "tags", autoResultMap = true)
public class TagsPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 主体类型，user，team
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * 标签类型，agent：AGENT-智能体, knowledge：KNOWLEDGE-知识库, general_tool：GENERAL_TOOL-通用工具, workflow_tool：WORKFLOW_TOOL-工作流工具, custom_tool：CUSTOM_TOOL-自定义工具
     */
    @TableField("tag_type")
    private TagTypeEnum tagType;

    /**
     * 标签名称
     */
    @TableField("name")
    private String name;

    /**
     * 图标链接
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

