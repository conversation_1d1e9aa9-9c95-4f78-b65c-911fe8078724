package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@TableName(value = "rtc_pitfall_info", autoResultMap = true)
public class RTCPitfallInfoPO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 智能体id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 隐患编号
     */
    @TableField("pitfall_no")
    private String pitfallNo;

    /**
     * 状态,1:UN_SUBMIT-待上报,2:TBC-待确认,3:UN_RESOLVE-待解决,4:RESOLVED-已解决,5:无需解决
     */
    @TableField("status")
    private RTCPitfallStatusEnum status;

    /**
     * 隐患区域
     */
    @TableField("pitfall_area")
    private String pitfallArea;

    /**
     * 隐患上传内容
     */
    @TableField(value = "upload_content", typeHandler = JacksonTypeHandler.class)
    private List<String> uploadContent;

    /**
     * 上报时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 确认时间
     */
    @TableField("confirm_time")
    private LocalDateTime confirmTime;

    /**
     * 解决时间
     */
    @TableField("resolve_time")
    private LocalDateTime resolveTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;
}
