package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommonSearch;
import cn.genn.ai.hub.app.domain.auth.model.valobj.AuthUniqueCriteriaEnum;
import cn.genn.ai.hub.app.domain.auth.repository.IAuthCommonRepository;
import cn.genn.ai.hub.app.infrastructure.converter.AuthCommonConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AuthCommonMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.AuthCommonPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.database.mybatisplus.query.QueryWrapperUtil;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class AuthCommonRepositoryImpl extends ServiceImpl<AuthCommonMapper, AuthCommonPO> implements IAuthCommonRepository {

    private final AuthCommonMapper mapper;
    private final AuthCommonConverter converter;

    @Override
    public void deleteByUniqueCriteria(List<AuthCommon> authList, Map<AuthUniqueCriteriaEnum, Object> uniqueCriteria) {
        QueryWrapper<AuthCommonPO> queryWrapper = buildQueryWrapper(uniqueCriteria);
        mapper.delete(queryWrapper);
    }

    @Override
    public void fullModifyByUniqueCriteria(List<AuthCommon> authList, Map<AuthUniqueCriteriaEnum, Object> uniqueCriteria) {
        deleteByUniqueCriteria(authList, uniqueCriteria);
        List<AuthCommonPO> authCommonPOList = converter.entity2PO(authList);
        saveBatch(authCommonPOList);
    }

    @Override
    public void addAuth(List<AuthCommon> authList) {
        List<AuthCommonPO> authCommonPOList = converter.entity2PO(authList);
        saveBatch(authCommonPOList);
    }

    @Override
    @IgnoreTenant
    public List<AuthCommon> list(AuthCommonSearch query) {
        if (query.getTenantId() == null) {
            query.setTenantId(CurrentUserHolder.getTenantId());
        }
        QueryWrapper<AuthCommonPO> queryWrapper = QueryWrapperUtil.build(query);
        List<AuthCommonPO> authCommonPOS = mapper.selectList(queryWrapper);
        return converter.PO2Entity(authCommonPOS);
    }

    private QueryWrapper<AuthCommonPO> buildQueryWrapper(Map<AuthUniqueCriteriaEnum, Object> uniqueCriteria) {
        QueryWrapper<AuthCommonPO> queryWrapper = new QueryWrapper<>();
        uniqueCriteria.forEach((key, value) -> {
            if (value instanceof List) {
                queryWrapper.in(key.getCode(), (List<?>) value);
            } else {
                queryWrapper.eq(key.getCode(), value);
            }
        });
        return queryWrapper;
    }
}
