package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GennAIFileStorageProperties {

    private String endpoint;
    private String region;
    private String accessKey;
    private String secretKey;
    private String bucketName;
    private String domain;
    /**
     * TOS对应的桶的IAM角色配置 key:桶名 value:IAM角色
     */
    private String iamRole;
    /**
     * 自定义域名
     */
    private String customDomain;
}
