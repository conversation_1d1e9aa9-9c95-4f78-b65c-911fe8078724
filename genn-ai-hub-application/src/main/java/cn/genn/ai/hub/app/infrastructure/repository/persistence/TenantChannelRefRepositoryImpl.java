package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.TenantChannelRefMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.TenantChannelRefPO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description 租户渠道仓储层实现
 * @date 2025-04-25
 */
@Repository
public class TenantChannelRefRepositoryImpl extends ServiceImpl<TenantChannelRefMapper, TenantChannelRefPO> {
}
