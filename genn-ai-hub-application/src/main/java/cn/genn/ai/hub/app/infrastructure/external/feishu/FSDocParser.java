package cn.genn.ai.hub.app.infrastructure.external.feishu;

import cn.genn.ai.hub.app.application.dto.feishu.*;
import cn.genn.ai.hub.app.application.enums.feishu.BlockType;
import cn.genn.ai.hub.app.application.query.FSSheetContentQuery;
import cn.genn.ai.hub.app.application.query.FSSheetsQuery;
import cn.genn.ai.hub.app.application.service.FSWikiFileService;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.lark.oapi.Client;
import com.lark.oapi.service.docx.v1.model.*;
import com.lark.oapi.service.drive.v1.model.BatchGetTmpDownloadUrlMediaReq;
import com.lark.oapi.service.drive.v1.model.BatchGetTmpDownloadUrlMediaResp;
import com.lark.oapi.service.sheets.v3.model.GridProperties;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class FSDocParser {

    private final Long repoId;
    private final String appId;
    private final String documentId;
    private final Client client;

    public FSDocParser(Long repoId, String appId, String documentId, Client client) {
        this.repoId = repoId;
        this.appId = appId;
        this.documentId = documentId;
        this.client = client;
    }

    private static final Logger log = LoggerFactory.getLogger(FSDocParser.class);


    private static final Map<String, Block> BLOCK_MAP = new HashMap<>(256);
    private static final Map<String, Integer> AUTO_ORDER = new HashMap<>(4);

    public FSAnalysisResult doParser(ListDocumentBlockRespBody blockResp) {
        FSAnalysisResult result = new FSAnalysisResult();
        result.setDocumentId(documentId);
        try {
            // 读取 JSON 文件
            List<Block> blocks = Arrays.asList(blockResp.getItems());
            if (CollUtil.isEmpty(blocks)) {
                return result;
            }
            Block first = blocks.getFirst();
            StringBuilder markdown = new StringBuilder();
            TextElement[] elements = first.getPage().getElements();
            StringBuilder content = new StringBuilder();
            for (TextElement element : elements) {
                TextRun textRun = element.getTextRun();
                if (textRun == null) {
                    continue;
                }
                content.append(textRun.getContent());
            }
            // 文档页标题
            String title = content.toString();
            result.setName(StringUtils.isBlank(title) ? "未命名.md" : title + ".md");
            markdown.append(title).append("\n\n");

            Map<String, Block> blockMap = blocks
                .stream()
                .collect(Collectors.toMap(Block::getBlockId, Function.identity()));
            BLOCK_MAP.putAll(blockMap);
            String[] children = first.getChildren();
            for (String blockId : children) {
                Block block = blockMap.get(blockId);
                processBlock(block, markdown, 1);
            }
            result.setMarkdown(markdown.toString());
            return result;
        } catch (Exception e) {
            log.error("解析文件格式异常", e);
        } finally {
            BLOCK_MAP.clear();
            AUTO_ORDER.clear();
        }
        return result;
    }

    /**
     * @param block    快
     * @param markdown 拼接内容
     * @param level    层级
     */
    private void processBlock(Block block, StringBuilder markdown, Integer level) {
        if (block == null) {
            return;
        }
        int blockTypeCode = block.getBlockType();
        BlockType blockType = BlockType.getByCode(blockTypeCode);
        if (blockType != null) {
            switch (blockType) {
                case HEADING1:
                    markdown.append(parseHeading(block, 1));
                    break;
                case HEADING2:
                    markdown.append(parseHeading(block, 2));
                    break;
                case HEADING3:
                    markdown.append(parseHeading(block, 3));
                    break;
                case HEADING4:
                    markdown.append(parseHeading(block, 4));
                    break;
                case HEADING5:
                    markdown.append(parseHeading(block, 5));
                    break;
                case HEADING6:
                    markdown.append(parseHeading(block, 6));
                    break;
                case HEADING7:
                    markdown.append(parseHeading(block, 7));
                    break;
                case HEADING8:
                    markdown.append(parseHeading(block, 8));
                    break;
                case HEADING9:
                    markdown.append(parseHeading(block, 9));
                    break;
                case ORDERED:
                    markdown.append(parseOrderedList(block, level));
                    break;
                case BULLET:
                    markdown.append(parseBulletList(block, level));
                    break;
                case TABLE:
                    markdown.append(parseTable(block));
                    break;
                case SHEET:
                    markdown.append(parseSheet(block));
                    break;
                case IMAGE:
                    markdown.append(parseImage(block));
                    break;
                case CODE:
                    markdown.append(parseCodeBlock(block));
                    break;
                case DIVIDER:
                    markdown.append("---\n");
                    break;
                case TEXT:
                    markdown.append(parseText(block));
                    break;
                case TODO:
                    markdown.append(parseTodo(block));
                    break;
                case CALLOUT:
                    markdown.append(parseCallout(block));
                    break;
                case VIEW:
                    markdown.append(parseView(block));
                    break;
                case FILE:
                    markdown.append(parseFile(block));
                    break;
                case QUOTE_CONTAINER:
                    markdown.append("> ");
                    break;
                default:
                    // 其他类型暂不处理
                    break;
            }
        }
        level++;
        // 处理子节点
        String[] children = block.getChildren();
        if (children != null && !BlockType.isTable(blockTypeCode)) {
            for (String child : children) {
                processBlock(BLOCK_MAP.get(child), markdown, level);
            }
        }
    }

    private String parseFile(Block block) {
        File file = block.getFile();
        String name = file.getName();
        String token = file.getToken();
        FSDownload.Info downLoadInfo = getDownLoadInfo(token);
        // todo 处理文件
        return "[" + name + "](" + downLoadInfo.getTmpDownloadUrl() + ")\n\n";
    }

    private String parseView(Block block) {
        View view = block.getView();
        Integer viewType = view.getViewType();
        if (viewType == 1) {
            // 卡片视图，独占一行
            return "> 卡片视图\n\n";
        }
        return null;
    }

    private String parseHeading(Block block, int level) {
        Text text = (Text) ReflectUtil.getFieldValue(block, "heading" + level);
        StringBuilder headingMarkdown = new StringBuilder();
        for (int i = 0; i < level; i++) {
            headingMarkdown.append("#");
        }
        headingMarkdown.append(" ");
        TextElement[] elements = text.getElements();
        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            if (textRun == null) {
                continue;
            }
            String content = textRun.getContent();
            headingMarkdown.append(content);
        }
        headingMarkdown.append("\n\n");
        return headingMarkdown.toString();
    }

    private String parseOrderedList(Block block, Integer level) {
        Text ordered = block.getOrdered();
        TextElement[] elements = ordered.getElements();
        TextStyle style = ordered.getStyle();
        String sequence = style.getSequence();
        if (StringUtils.equals(sequence, "auto")) {
            AUTO_ORDER.computeIfAbsent(block.getParentId(), k -> 1); // 如果不存在，则设置为 0
            int currentOrder = AUTO_ORDER.get(block.getParentId());
            AUTO_ORDER.put(block.getParentId(), currentOrder + 1); // 更新序号
            sequence = String.valueOf(currentOrder);
        }
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < level - 1; i++) {
            sb.append("    ");
        }
        sb.append(sequence).append(". ");
        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            if (textRun == null) {
                continue;
            }
            sb.append(textRun.getContent());
        }
        sb.append("\n");
        return sb.toString();
    }

    private String parseBulletList(Block block, Integer level) {
        Text bullet = block.getBullet();
        TextElement[] elements = bullet.getElements();

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < level - 1; i++) {
            sb.append("    ");
        }
        sb.append("- ");
        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            if (textRun == null) {
                continue;
            }
            sb.append(textRun.getContent());
        }
        sb.append("\n");
        return sb.toString();
    }

    private String parseTable(Block block) {
        Table table = block.getTable();
        TableProperty property = table.getProperty();
        String[] cells = table.getCells();
        int columnSize = property.getColumnSize();
        int rowSize = property.getRowSize();

        StringBuilder tableMarkdown = new StringBuilder();
        // 构建表头
        tableMarkdown.append("|");
        for (int i = 0; i < columnSize; i++) {
            String cellContent = getCellContent(cells[i]);
            tableMarkdown.append(" ").append(cellContent).append(" |");
        }
        tableMarkdown.append("\n");

        // 构建分隔行
        tableMarkdown.append("|");
        for (int i = 0; i < columnSize; i++) {
            tableMarkdown.append(" --- |");
        }
        tableMarkdown.append("\n");

        // 填充数据
        int cellIndex = columnSize; // 从表头之后的数据开始填充
        for (int i = 0; i < rowSize - 1; i++) { // 减去表头行
            tableMarkdown.append("|");
            for (int j = 0; j < columnSize; j++) {
                if (cellIndex < cells.length) {
                    String cellContent = getCellContent(cells[cellIndex]);
                    tableMarkdown.append(" ").append(cellContent).append(" |");
                    cellIndex++;
                } else {
                    tableMarkdown.append("  |");
                }
            }
            tableMarkdown.append("\n");
        }

        tableMarkdown.append("\n");
        return tableMarkdown.toString();
    }


    private String parseSheet(Block block) {
        try {
            Sheet sheet = block.getSheet();
            String token = sheet.getToken();
            String[] split = StringUtils.split(token, "_");

            FSWikiFileService wikiFileService = SpringUtil.getBean(FSWikiFileService.class);
            FSSheetsQuery sheetsQuery = new FSSheetsQuery();
            sheetsQuery.setAppId(this.appId);
            sheetsQuery.setObjToken(split[0]);

            SheetListBody sheetList = wikiFileService.getSheetList(sheetsQuery.getAppId(), sheetsQuery.getObjToken());
            int colum = 1;
            int row = 1;
            for (com.lark.oapi.service.sheets.v3.model.Sheet sheetListSheet : sheetList.getSheets()) {
                if (StringUtils.equals(sheetListSheet.getSheetId(), split[1])) {
                    GridProperties gridProperties = sheetListSheet.getGridProperties();
                    colum = gridProperties.getColumnCount();
                    row = gridProperties.getRowCount();
                    break;
                }
            }
            // 使用 ExcelColumnConverter 转换列索引为 Excel 列标识符
            String left = FSUtil.toExcelColumnName(0);
            String right = FSUtil.toExcelColumnName(colum - 1);
            String ranges = left + "1:" + right + row;
            FSSheetContentQuery contentQuery = FSSheetContentQuery
                .builder()
                .repoId(this.repoId)
                .objToken(split[0])
                .sheetId(split[1])
                .ranges(ranges)
                .build();
            SheetDataResp sheetContent = wikiFileService.getSheetContent(contentQuery);
            WikiSheetCell valueRange = sheetContent.getValueRange();
            // 表格内容
            List<List<Object>> values = valueRange.getValues();
            return FSUtil.convertToMarkdownTable(values);
        } catch (Exception e) {
            log.error("获取表格内容失败：", e);
        }
        return StringUtils.EMPTY;
    }


    // 表格字块中解析
    private String getCellContent(String cellId) {
        StringBuilder contents = new StringBuilder();
        Block block = BLOCK_MAP.get(cellId);
        if (block == null) {
            return "";
        }
        String[] children = block.getChildren();
        for (String child : children) {
            Block childBlock = BLOCK_MAP.get(child);
            if (childBlock == null) {
                continue;
            }
            Text text = childBlock.getText();
            TextElement[] elements = text.getElements();
            for (TextElement element : elements) {
                TextRun textRun = element.getTextRun();
                if (textRun == null) {
                    continue;
                }
                String content = textRun.getContent();
                contents.append(content);
            }
        }
        return contents.toString();
    }

    private String parseImage(Block block) {
        Image image = block.getImage();
        String token = image.getToken();
        FSDownload.Info first = getDownLoadInfo(token);
        if (first == null) return "";
        // todo 这里假设 token 是图片链接，实际可能需要根据具体情况处理
        return "![图片](" + first.getTmpDownloadUrl() + ")\n\n";
    }

    private FSDownload.Info getDownLoadInfo(String token) {

        // 创建请求对象
        BatchGetTmpDownloadUrlMediaReq req = BatchGetTmpDownloadUrlMediaReq.newBuilder().fileTokens(new String[]{token}).build();

        // 发起请求
        BatchGetTmpDownloadUrlMediaResp resp = null;
        try {
            resp = client.drive().v1().media().batchGetTmpDownloadUrl(req);
        } catch (Exception e) {
            log.error("调飞书接口异常", e);
        }

        // 处理服务端错误
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            return null;
        }

        // 业务数据处理
        List<FSDownload> fsDownloads = JsonUtils.parseToList(JsonUtils.toJson(resp.getData()), FSDownload.class);
        List<FSDownload.Info> tmpDownloadUrls = fsDownloads.getFirst().getTmpDownloadUrls();
        return tmpDownloadUrls.getFirst();
    }

    private static String parseCodeBlock(Block block) {
        Text code = block.getCode();
        TextElement[] elements = code.getElements();
        StringBuilder content = new StringBuilder();
        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            if (textRun == null) {
                continue;
            }
            content.append(textRun.getContent());
        }
        String codeData = content.toString();
        return "```\n" + codeData + "\n```\n\n";
    }

    private static String parseText(Block block) {
        Text text = block.getText();
        TextElement[] elements = text.getElements();
        StringBuilder contentBuilder = new StringBuilder();

        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            if (textRun == null) {
                continue;
            }
            String content = textRun.getContent();
            TextElementStyle textElementStyle = textRun.getTextElementStyle();
            Link link = textElementStyle.getLink();
//            if (textElementStyle.getBold()) {
//                content = "__" + content + "__";
//            }
            if (link != null) {
                String url = link.getUrl();
                // 解码 URL
                url = java.net.URLDecoder.decode(url, StandardCharsets.UTF_8);
                contentBuilder.append("[").append(content).append("](").append(url).append(")");
            } else {
                contentBuilder.append(content);
            }
            contentBuilder.append(" ");
        }

        return contentBuilder.toString().trim() + "\n\n";
    }

    private static String parseTodo(Block block) {
        Text todo = block.getTodo();
        TextElement[] elements = todo.getElements();
        StringBuilder content = new StringBuilder();
        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            if (textRun == null) {
                continue;
            }
            content.append(textRun.getContent());
        }
        String todoData = content.toString();
        return "- [ ] " + todoData + "\n\n";
    }

    private static String parseCallout(Block block) {
        // 暂未实现具体解析逻辑，可根据需求补充
        return "<| 高亮块内容待解析 |>\n\n";
    }
}
