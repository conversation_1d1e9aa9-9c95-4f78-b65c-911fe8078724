package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.enums.SensitiveWordType;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.SensitiveWordMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.SensitiveWordPO;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 敏感词仓储层实现
 * @date 2025-05-16
 */
@Repository
public class SensitiveWordRepositoryImpl extends ServiceImpl<SensitiveWordMapper, SensitiveWordPO> {

    @IgnoreTenant
    public boolean isWordUnique(String words, SensitiveWordType wordsType, Long teamId, Long createUserId, Long excludeSensitiveWordId) {
        if (words == null || words.trim().isEmpty()) {
            // 空敏感词不进行重复校验，或者根据业务需求决定是否允许空词
            return true;
        }

        LambdaQueryWrapper<SensitiveWordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SensitiveWordPO::getWords, words.trim()); // 比较 trim 后的词

        if (SensitiveWordType.SYSTEM.equals(wordsType)) {
            queryWrapper.eq(SensitiveWordPO::getWordsType, SensitiveWordType.SYSTEM);
        } else if (SensitiveWordType.USER.equals(wordsType)) {
            queryWrapper.eq(SensitiveWordPO::getWordsType, SensitiveWordType.USER);
            queryWrapper.eq(SensitiveWordPO::getTenantId, CurrentUserHolder.getTenantId()); // 用户词通常在租户内
            if (teamId != null) {
                // 团队空间下的敏感词校验
                queryWrapper.eq(SensitiveWordPO::getTeamId, teamId);
            } else {
                // 个人空间下的敏感词校验 (teamId is null, 结合 createUserId)
                queryWrapper.isNull(SensitiveWordPO::getTeamId)
                    .eq(SensitiveWordPO::getCreateUserId, createUserId);
            }
        }

        if (excludeSensitiveWordId != null) {
            // 更新时，排除自身
            queryWrapper.ne(SensitiveWordPO::getId, excludeSensitiveWordId);
        }

        return this.getBaseMapper().selectCount(queryWrapper) == 0;
    }

    @IgnoreTenant
    public List<SensitiveWordPO> findSensitiveWords() {
        return this.getBaseMapper()
            .selectList(new LambdaQueryWrapper<SensitiveWordPO>()
            .eq(SensitiveWordPO::getDeleted, DeletedTypeEnum.NOT_DELETED));
    }
}
