package cn.genn.ai.hub.app.infrastructure.repository.config.impl;

import cn.genn.ai.hub.app.infrastructure.repository.config.ModelConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 重排模型 rerank
 */
@Getter
@Setter
@ToString
public class RerankConfig implements ModelConfig {

    // 模型综合价格
    private BigDecimal charsPointsPrice;

    private Boolean isCustom;

    @Override
    public boolean validate() {
        return charsPointsPrice != null;
    }
}
