package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ChannelBaseMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ChannelBasePO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 智能体渠道仓储层实现
 * @date 2025-04-25
 */
@Repository
public class ChannelBaseRepositoryImpl extends ServiceImpl<ChannelBaseMapper, ChannelBasePO> {
    public ChannelBasePO selectByUniqueId(ChannelTypeEnum channelType, String channelUniqueIdentifier) {
        return this.lambdaQuery()
            .eq(ChannelBasePO::getChannelType, channelType)
            .eq(ChannelBasePO::getUniqueIdentifier, channelUniqueIdentifier)
            .one();
    }

    public List<ChannelBasePO> queryByChannelType(ChannelTypeEnum channelType) {
        return this.lambdaQuery()
            .eq(ChannelBasePO::getChannelType, channelType)
            .list();
    }

    public ChannelBasePO queryByChannelTypeAndUniqueKey(ChannelTypeEnum channelTypeEnum, String uniqueKey) {
        return this.lambdaQuery()
            .eq(ChannelBasePO::getChannelType, channelTypeEnum)
            .eq(ChannelBasePO::getUniqueIdentifier, uniqueKey)
            .one();
    }
}
