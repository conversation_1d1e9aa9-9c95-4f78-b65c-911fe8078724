package cn.genn.ai.hub.app.infrastructure.external.feishu.event;

import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

/**
 * <AUTHOR>
 * @description 飞书事件解析器
 * @date 2025-04-09
 */
public class FeishuEventParser {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final Gson GSON = new Gson();

    public static FeishuBaseEvent parse(String json) throws Exception {
        JsonNode root = MAPPER.readTree(json);

        // 判断版本
        JsonNode schemaNode = root.path("schema");
        if (schemaNode.isTextual() && "2.0".equals(schemaNode.asText())) {
            return MAPPER.treeToValue(root, FeishuV2Event.class);
        } else {
            return MAPPER.treeToValue(root, FeishuV1Event.class);
        }
    }

    public static <T> T parseEvent(Object eventObj, Class<T> eventType) {
        String json = JsonUtils.toJson(eventObj); // 转换为标准 JSON 字符串
        return GSON.fromJson(json, eventType);
    }

    public static <T> T parseEvent(String json, Class<T> eventType) {
        return GSON.fromJson(json, eventType);
    }
}
