package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RerankerProperties {

    /**
     * 重排序策略
     */
    private RerankerStrategy strategy = RerankerStrategy.WEIGHTED;

    /**
     * 语义检索权重,仅在策略为WEIGHTED时生效
     */
    private float semanticWeight = 0.75f;

    /**
     * 全文检索权重,仅在策略为WEIGHTED时生效
     */
    private float keywordWeight = 0.25f;

    /**
     * RRF策略的K值,仅在策略为RRF时生效
     */
    private int k = 100;


    public enum RerankerStrategy {

        /**
         * WeightedRanker 策略根据每个向量字段的重要性，为每个向量检索路径的结果分配不同的权重。
         * 当每个向量字段的重要性不同时，就会应用这种 Rerankers 策略，
         * 这样就可以通过给某些向量字段分配更高的权重，使其比其他向量字段更受重视。
         * 例如，在多模态搜索中，文本描述可能比图像中的颜色分布更重要。
         */
        WEIGHTED,

        /**
         * RRF 是一种数据融合方法，它根据排名的倒数来组合排名列表。它是一种平衡各向量场影响的有效方法，尤其是在没有明确的重要性优先顺序时。
         * 这种策略通常用于想要对所有向量场给予同等考虑，或对每个场的相对重要性存在不确定性时。
         */
        RRF
    }

}
