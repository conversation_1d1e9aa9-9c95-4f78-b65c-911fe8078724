package cn.genn.ai.hub.app.infrastructure.exception;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.MessageCodeWrap;

/**
 * <AUTHOR>
 * @description 智能体调用异常
 * @date 2025-05-14
 */
public class AgentInvokeException extends BaseException {

    public AgentInvokeException() {
        super(AgentInvokeErrorCode.FAIL);
    }
    public AgentInvokeException(String message) {
        super(AgentInvokeErrorCode.FAIL.buildCode(), message);
    }

    public AgentInvokeException(String code, String message) {
        super(code, message);
    }

    public AgentInvokeException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public AgentInvokeException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
