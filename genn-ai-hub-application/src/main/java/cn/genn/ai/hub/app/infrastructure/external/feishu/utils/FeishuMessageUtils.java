package cn.genn.ai.hub.app.infrastructure.external.feishu.utils;

import cn.genn.ai.hub.app.application.enums.feishu.ChatTypeEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.json.JSONUtil;
import com.lark.oapi.service.im.v1.enums.CreateMessageReceiveIdTypeEnum;
import com.lark.oapi.service.im.v1.enums.MsgTypeEnum;
import com.lark.oapi.service.im.v1.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description 飞书消息工具类
 * @date 2025-04-10
 */
@Slf4j
public class FeishuMessageUtils {

    // 统一消息构建方法
    public static CreateMessageReq buildMessageRequest(String chatId, Object content) {
        return buildMessageRequest(chatId, content, MsgTypeEnum.MSG_TYPE_TEXT);
    }

    public static CreateMessageReq buildMessageRequest(String chatId, Object content, MsgTypeEnum msgType) {
        String contentStr = content instanceof String ? (String) content : JsonUtils.toJson(content);
        return CreateMessageReq.newBuilder()
            .receiveIdType(CreateMessageReceiveIdTypeEnum.CHAT_ID)
            .createMessageReqBody(CreateMessageReqBody.newBuilder()
                .receiveId(chatId)
                .msgType(msgType.getValue())
                .content(contentStr)
                .build())
            .build();
    }

    // 增强的日志记录
    public static void logMessageResponse(String chatId, CreateMessageResp response) {
        if (response == null) {
            log.error("Empty response received | chatId:{}", chatId);
            return;
        }

        if (!response.success()) {
            log.error("Message failed | chatId:{} | code:{} | msg:{} | requestId:{}",
                chatId, response.getCode(), response.getMsg(), response.getRequestId());
        }
    }

    // 安全的用户输入提取
    public static String extractUserInput(P2MessageReceiveV1Data messageData) {
        return Optional.ofNullable(messageData)
            .map(P2MessageReceiveV1Data::getMessage)
            .map(message -> JSONUtil.parseObj(message.getContent()))
            .map(json -> json.getStr("text"))
            .map(text -> isGroupChat(messageData) ? removeMentions(text) : text.trim())
            .orElse("");
    }

    public static String generateChatId(ChatTypeEnum chatType, String chatId, String openId) {
        if (ChatTypeEnum.P2P.equals(chatType)) {
            return chatId;
        } else if (ChatTypeEnum.GROUP.equals(chatType)) {
            return String.join(":", chatId, openId);
        }
        return chatId;
    }

    private static boolean isGroupChat(P2MessageReceiveV1Data messageData) {
        return Optional.ofNullable(messageData)
            .map(P2MessageReceiveV1Data::getMessage)
            .map(EventMessage::getChatType)
            .filter("group"::equalsIgnoreCase)
            .isPresent();
    }

    private static String removeMentions(String text) {
        return text.replaceAll("@\\S+", "").trim();
    }
}
