package cn.genn.ai.hub.app.infrastructure.converter;

import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.infrastructure.repository.po.AuthCommonPO;
import cn.genn.core.model.converter.POConverter;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AuthCommonConverter extends POConverter<AuthCommon, AuthCommonPO> {

    AuthCommonConverter INSTANCE = Mappers.getMapper(AuthCommonConverter.class);

}
