package cn.genn.ai.hub.app.infrastructure.config;


import cn.genn.core.context.BaseRequestContext;


/**
 * 请求上下文
 * 生命周期为一次请求
 * <AUTHOR>
 */
public class GennRequestContext extends BaseRequestContext {

    public static GennRequestContext getContext() {
        return (GennRequestContext) get();
    }
    public static void setContext(GennRequestContext context) {
        set(context);
    }

    public static void initTenantId(Long tenantId) {
        BaseRequestContext context = BaseRequestContext.get();
        if (context != null) {
            context.setTenantId(tenantId);
        } else {
            GennRequestContext gennRequestContext = new GennRequestContext();
            gennRequestContext.setTenantId(tenantId);
            gennRequestContext.setUserId(0L);
            gennRequestContext.setUserName("系统");
            set(gennRequestContext);
        }
    }
}
