package cn.genn.ai.hub.app.infrastructure.repository.mapper;

import cn.genn.ai.hub.app.application.dto.question.DepartmentQuestionCountDTO;
import cn.genn.ai.hub.app.application.dto.question.DepartmentUserDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionAnalysisDTO;
import cn.genn.ai.hub.app.application.query.DepartmentQuestionCountQuery;
import cn.genn.ai.hub.app.application.query.QuestionAnalysisQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionAnalysisPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QuestionAnalysisMapper extends BaseMapper<QuestionAnalysisPO> {

    List<QuestionAnalysisDTO> getAnalysis(@Param("query") QuestionAnalysisQuery query);

    List<String> getAllTypes(@Param("appId") String appId);
}
