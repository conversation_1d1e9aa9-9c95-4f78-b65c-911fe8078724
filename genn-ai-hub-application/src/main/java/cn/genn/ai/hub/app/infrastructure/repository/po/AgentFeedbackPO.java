package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.FeedbackSourceSystemEnum;
import cn.genn.ai.hub.app.application.enums.FeedbackTypeEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * AgentFeedbackPO对象
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "agent_feedback", autoResultMap = true)
public class AgentFeedbackPO {

    /**
     * 主键ID，自增长
     */
    @TableId
    private Long id;

    /**
     * 工作流id
     */
    @TableField("workflow_id")
    private String workflowId;

    /**
     * 会话id
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 单论对话id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 反馈类型: LIKE (赞)、DISLIKE (踩)、WORD_MARK（划词反馈）
     */
    @TableField("feedback_type")
    private FeedbackTypeEnum feedbackType;

    /**
     * 反馈标签
     */
    @TableField("feedback_tag")
    private String feedbackTag;

    /**
     * 划词部分，仅反馈类型为划词反馈时有值
     */
    @TableField("word_mark_text")
    private String wordMarkText;

    /**
     * 用户提供的具体原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 来源系统
     */
    @TableField("source_system")
    private FeedbackSourceSystemEnum sourceSystem;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

