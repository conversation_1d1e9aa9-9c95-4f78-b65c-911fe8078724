package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.command.KbRepoQaPairEditCommand;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoQaPairMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoQaPairPO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 知识库问答对Repository实现类
 * <AUTHOR>
 */
@Slf4j
@Repository
public class KbRepoQaPairRepositoryImpl extends ServiceImpl<KbRepoQaPairMapper, KbRepoQaPairPO> {

    @Resource
    private KbRepoQaPairMapper qaPairMapper;

    public KbRepoQaPairPO getQaPairByQaPairKey(String qaPairKey) {
        return qaPairMapper.selectOne(new UpdateWrapper<KbRepoQaPairPO>().lambda().eq(KbRepoQaPairPO::getQaPairKey, qaPairKey));
    }

    public KbRepoQaPairPO getQaPairById(Long id) {
        return qaPairMapper.selectById(id);
    }

    public List<KbRepoQaPairPO> getQaPairListByQaPairKeyList(List<String> qaPairKeyList) {
        return qaPairMapper.selectList(new UpdateWrapper<KbRepoQaPairPO>().lambda().in(KbRepoQaPairPO::getQaPairKey, qaPairKeyList));
    }

    public List<KbRepoQaPairPO> getQaPairListByIds(List<Long> ids) {
        return qaPairMapper.selectList(new UpdateWrapper<KbRepoQaPairPO>().lambda().in(KbRepoQaPairPO::getId, ids));
    }

    public Boolean editQaPair(KbRepoQaPairPO po) {
        KbRepoQaPairPO qaPairPO = this.getById(po.getId());
        if (qaPairPO == null) {
            return false;
        }
        return this.updateById(po);
    }

    public Boolean batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return this.removeBatchByIds(ids);
    }

    public Boolean batchInsert(List<KbRepoQaPairPO> poList) {
        return this.saveBatch(poList, 10);
    }

    public void updateHandleStatusOfQaPairKey(String qaPairKey, HandleStatusEnum handleStatusEnum) {
        UpdateWrapper<KbRepoQaPairPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(KbRepoQaPairPO::getQaPairKey, qaPairKey)
                .set(KbRepoQaPairPO::getHandleStatus, handleStatusEnum);
        this.update(updateWrapper);
    }

    public Long insert(KbRepoQaPairPO po) {
        qaPairMapper.insert(po);
        return po.getId();
    }
}
