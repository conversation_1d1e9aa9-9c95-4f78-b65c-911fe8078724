package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * QuestionAnalysisPO对象
 *
 * <AUTHOR>
 * @desc 高频问题分析统计表
 */
@Data
@TableName(value = "question_analysis", autoResultMap = true)
public class QuestionAnalysisPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 工作流id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 问题精炼
     */
    @TableField("concise")
    private String concise;

    /**
     * 问题类型
     */
    @TableField("type")
    private String type;

    /**
     * 问题出现次数
     */
    @TableField("count")
    private Integer count;

    /**
     * 关联问题ID-数组[id1, id2, id3...]
     */
    @TableField("rel_info_id")
    private String relInfoId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

