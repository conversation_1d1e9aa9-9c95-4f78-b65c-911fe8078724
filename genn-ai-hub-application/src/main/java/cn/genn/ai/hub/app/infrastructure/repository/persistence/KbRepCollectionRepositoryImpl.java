package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoCollectionAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionListQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCollectionMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Date: 2025/3/7
 * @Author: kangjian
 */
@Repository
public class KbRepCollectionRepositoryImpl extends ServiceImpl<KbRepoCollectionMapper, KbRepoCollectionPO> {

    @Resource
    private KbRepoCollectionMapper mapper;
    @Resource
    private KbRepoCollectionAssembler assembler;


    /**
     * 根据知识库查询下面的所有的数据集
     */
    public Page<KbRepoCollectionPO> pageKbRepoCollection(KbRepoCollectionListQuery query) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 如果传了pid 就查询pid下的 如果没传就默认是 0 父级为0
            .eq(Objects.isNull(query.getPid()), KbRepoCollectionPO::getPid, 0L)
            .eq(Objects.nonNull(query.getPid()), KbRepoCollectionPO::getPid, query.getPid())
            .eq(Objects.nonNull(query.getRepoId()), KbRepoCollectionPO::getRepoId, query.getRepoId())
            .like(StrUtil.isNotBlank(query.getName()), KbRepoCollectionPO::getName, query.getName())
            .eq(KbRepoCollectionPO::getDeleted, DeletedEnum.NOT_DELETED)
            .orderByDesc(KbRepoCollectionPO::getCreateTime);
        return mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper);
    }


    @IgnoreTenant
    public List<KbRepoCollectionDTO> selectCollectByRepoId(Long repoId, Collection<Long> collIds) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .in(CollUtil.isNotEmpty(collIds), KbRepoCollectionPO::getId, collIds)
            .eq(KbRepoCollectionPO::getRepoId, repoId)
            .eq(KbRepoCollectionPO::getEnabled, true)
            .eq(KbRepoCollectionPO::getDeleted, DeletedEnum.NOT_DELETED);

        return assembler.PO2DTO(mapper.selectList(queryWrapper));
    }

    public List<KbRepoCollectionDTO> selectDisableCollectByRepoId(Collection<Long> repoId) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .in(KbRepoCollectionPO::getRepoId, repoId)
            .eq(KbRepoCollectionPO::getEnabled, false)
            .eq(KbRepoCollectionPO::getDeleted, DeletedEnum.NOT_DELETED);

        return assembler.PO2DTO(mapper.selectList(queryWrapper));
    }

    public List<KbRepoCollectionDTO> selectCollectionByPId(Long pid) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoCollectionPO::getPid, pid)
            .eq(KbRepoCollectionPO::getDeleted, DeletedEnum.NOT_DELETED);

        return assembler.PO2DTO(mapper.selectList(queryWrapper));
    }

    public Boolean removeByCollId(Long id) {
        UpdateWrapper<KbRepoCollectionPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoCollectionPO::getId, id)
            .set(KbRepoCollectionPO::getDeleted, DeletedEnum.DELETED);
        return update(updateWrapper);
    }

    @IgnoreTenant
    public List<KbRepoCollectionDTO> selectCollectionByIds(List<Long> collectionIds) {
        return assembler.PO2DTO(mapper.selectByIds(collectionIds));
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }

    @IgnoreTenant
    public void updateDataSizeById(Long repoId, Long collId, Long dataSize) {
        UpdateWrapper<KbRepoCollectionPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoCollectionPO::getId, collId)
            .eq(KbRepoCollectionPO::getRepoId, repoId)
            .set(KbRepoCollectionPO::getDataSize, dataSize);
        update(updateWrapper);
    }

    public void updateByDTO(KbRepoCollectionDTO update) {
        KbRepoCollectionPO newPO = assembler.DTO2PO(update);
        updateById(newPO);
    }


    @IgnoreTenant
    public List<KbRepoCollectionDTO> collIdsByRepoId(Long repoId) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoCollectionPO::getRepoId, repoId)
            .eq(KbRepoCollectionPO::getEnabled, true)
            .eq(KbRepoCollectionPO::getDeleted, DeletedEnum.NOT_DELETED);
        return assembler.PO2DTO(list(queryWrapper));
    }

    @IgnoreTenant
    public List<KbRepoCollectionDTO> selectCollByRepoIdAndName(List<Long> repoIds, String date) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .in(CollUtil.isNotEmpty(repoIds), KbRepoCollectionPO::getRepoId, repoIds)
            .like(KbRepoCollectionPO::getName, date)
            .eq(KbRepoCollectionPO::getEnabled, true)
            .eq(KbRepoCollectionPO::getDeleted, DeletedEnum.NOT_DELETED);
        return assembler.PO2DTO(list(queryWrapper));
    }
}
