package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.TeamUserRefMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.TeamUserRefPO;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 团队用户关联仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class TeamUserRefRepositoryImpl extends ServiceImpl<TeamUserRefMapper, TeamUserRefPO> {

    @Resource
    private TeamUserRefMapper teamUserRefMapper;

    /**
     * 获取用户加入的团队ID列表
     *
     * @param userId 用户ID
     * @return 团队ID列表
     */
    public Set<Long> getJoinedTeamIds(Long userId) {
        LambdaQueryWrapper<TeamUserRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamUserRefPO::getUserId, userId);
        List<TeamUserRefPO> teamUserRefs = teamUserRefMapper.selectList(queryWrapper);
        return teamUserRefs.stream()
                .map(TeamUserRefPO::getTeamId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取团队成员数量
     *
     * @param teamId 团队ID
     * @return 成员数量
     */
    public Long countTeamMembers(Long teamId) {
        LambdaQueryWrapper<TeamUserRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamUserRefPO::getTeamId, teamId);
        return teamUserRefMapper.selectCount(queryWrapper);
    }

    /**
     * 批量获取团队成员数量
     *
     * @param teamIds 团队ID列表
     * @return 团队ID -> 成员数量的映射
     */
    public Map<Long, Long> batchCountTeamMembers(List<Long> teamIds) {
        if (teamIds == null || teamIds.isEmpty()) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<TeamUserRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamUserRefPO::getTeamId, teamIds);
        List<TeamUserRefPO> teamUserRefs = teamUserRefMapper.selectList(queryWrapper);

        return teamUserRefs.stream()
                .collect(Collectors.groupingBy(
                    TeamUserRefPO::getTeamId,
                    Collectors.collectingAndThen(Collectors.counting(), Long::valueOf)
                ));
    }

    /**
     * 添加团队成员
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否添加成功
     */
    public boolean addTeamMember(Long teamId, Long userId) {
        TeamUserRefPO teamUserRefPO = new TeamUserRefPO();
        teamUserRefPO.setTeamId(teamId);
        teamUserRefPO.setUserId(userId);
        return save(teamUserRefPO);
    }

    /**
     * 批量添加团队成员
     *
     * @param teamId 团队ID
     * @param userIds 用户ID列表
     */
    public Boolean batchAddTeamMember(Long teamId, List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return true;
        }
        List<TeamUserRefPO> existUserRefs = getTeamUserRefByTeamId(teamId);
        userIds.removeAll(existUserRefs.stream().map(TeamUserRefPO::getUserId).toList());
        if (userIds.isEmpty()) {
            return true;
        }
        List<TeamUserRefPO> teamUserRefPOS = userIds.stream().map(userId -> {
            TeamUserRefPO teamUserRefPO = new TeamUserRefPO();
            teamUserRefPO.setTeamId(teamId);
            teamUserRefPO.setUserId(userId);
            return teamUserRefPO;
        }).collect(Collectors.toList());
        return saveBatch(teamUserRefPOS);
    }

    /**
     * 移除团队成员
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否移除成功
     */
    public boolean removeTeamMember(Long teamId, Long userId) {
        LambdaQueryWrapper<TeamUserRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamUserRefPO::getTeamId, teamId)
                .eq(TeamUserRefPO::getUserId, userId);
        return remove(queryWrapper);
    }

    /**
     * 检查用户是否是团队成员
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否是团队成员
     */
    public boolean isMember(Long teamId, Long userId) {
        LambdaQueryWrapper<TeamUserRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamUserRefPO::getTeamId, teamId)
                .eq(TeamUserRefPO::getUserId, userId);
        return count(queryWrapper) > 0;
    }

    /**
     * 获取团队所有成员
     *
     * @param teamId 团队ID
     * @return 团队成员关联列表
     */
    public List<TeamUserRefPO> getTeamUserRefByTeamId(Long teamId) {
        LambdaQueryWrapper<TeamUserRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamUserRefPO::getTeamId, teamId);
        return list(queryWrapper);
    }


}
