package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.domain.mcp.model.entity.Mcp;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpRepository;
import cn.genn.ai.hub.app.infrastructure.converter.McpConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.McpMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpPO;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MCP仓储实现
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class McpRepositoryImpl extends ServiceImpl<McpMapper, McpPO> implements IMcpRepository {

    private final McpConverter mcpConverter;

    @Override
    public boolean save(Mcp mcp) {
        McpPO mcpPO = mcpConverter.toMcpPO(mcp);
        boolean result = super.save(mcpPO);
        if (result) {
            mcp.setId(mcpPO.getId());
        }
        return result;
    }

    @Override
    public Mcp getById(Long id) {
        McpPO mcpPO = super.getById(id);
        return mcpPO != null ? mcpConverter.toMcp(mcpPO) : null;
    }

    @Override
    public List<Mcp> listMcpsByIds(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        List<McpPO> mcpPOs = super.listByIds(ids);
        return mcpPOs.stream()
                .map(mcpConverter::toMcp)
                .collect(Collectors.toList());
    }

    @Override
    public List<Mcp> listAll() {
        LambdaQueryWrapper<McpPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McpPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        queryWrapper.eq(McpPO::getEnabled, BooleanTypeEnum.TRUE);
        List<McpPO> mcpPOs = list(queryWrapper);
        return mcpPOs.stream()
            .map(mcpConverter::toMcp)
            .collect(Collectors.toList());
    }

    @Override
    public boolean updateById(Mcp mcp) {
        McpPO mcpPO = mcpConverter.toMcpPO(mcp);
        return super.updateById(mcpPO);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

}
