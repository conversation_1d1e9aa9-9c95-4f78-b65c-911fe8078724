package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.core.model.enums.BooleanTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * ModelLogPO对象
 * 模型请求日志记录
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "model_log", autoResultMap = true)
public class ModelLogPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 请求唯一标识
     */
    @TableField("trace_id")
    private String traceId;

    /**
     * 模型ID
     */
    @TableField("model_id")
    private Long modelId;

    /**
     * 模型key
     */
    @TableField("model_key")
    private String modelKey;

    /**
     * 真实请求的模型
     */
    @TableField("model")
    private String model;

    /**
     * 回话ID
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 每次会话唯一标识
     */
    @TableField("data_id")
    private String dataId;

    /**
     * 渠道标识
     */
    @TableField("channel_id")
    private String channelId;

    /**
     * 渠道类型 no_login; api;  feishu;  wechat; online
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 请求来源：流程实例、插件、业务系统
     */
    @TableField("request_source")
    private String requestSource;

    /**
     * 请求key,根据来源分为流程id，插件requestId，业务系统标识
     */
    @TableField("request_key")
    private String requestKey;

    /**
     * 请求时间
     */
    @TableField("request_time")
    private LocalDateTime requestTime;

    /**
     * 请求ip
     */
    @TableField("request_ip")
    private String requestIp;

    /**
     * 请求体
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 请求token
     */
    @TableField("request_token_size")
    private Long requestTokenSize;

    /**
     * 响应体
     */
    @TableField("response_body")
    private String responseBody;

    /**
     * 响应token
     */
    @TableField("response_token_size")
    private Long responseTokenSize;

    /**
     * 是否成功
     */
    @TableField("success")
    private BooleanTypeEnum success;

    /**
     * 异常信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 耗时(毫秒)
     */
    @TableField("cost_time")
    private Long costTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

