package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * KbRepoDataPO对象
 *
 * <AUTHOR>
 * @desc 知识库数据集的数据条目
 */
@Data
@TableName(value = "kb_repo_data", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class KbRepoDataPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 数据类型 文本 图片 公式 表格
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 数据分块唯一标识
     */
    @TableField("data_key")
    private String dataKey;

    /**
     * 知识库 ID，标识数据所属的知识库
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 知识集合 ID，与 collection 表关联，表示数据所属的集合
     */
    @TableField("collection_id")
    private Long collectionId;

    /**
     * 问题文本（Question），用于嵌入或 QA 生成的核心内容
     */
    @TableField("question")
    private String question;

    /**
     * 答案文本（Answer），作为辅助内容或结构化提示的补充信息
     */
    @TableField("answer")
    private String answer;

    /**
     * 分块相对位置，表示数据在原始文档中的分块位置（从 0 开始）
     */
    @TableField("chunk_index")
    private Integer chunkIndex;

    /**
     * 权重，控制数据在训练或检索中的优先级（数值越高优先级越高）
     */
    @TableField("weight")
    private Integer weight;

    /**
     * 分词算法处理状态,0:WAIT-未处理, 2:DONE-已处理
     */
    @TableField("handle_status")
    private HandleStatusEnum handleStatus;

    /**
     * 重试次数，用于处理数据预处理失败时的重试机制
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 扩展数据 例如 图片的url
     */
    @TableField("ext_data")
    private String extData;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

