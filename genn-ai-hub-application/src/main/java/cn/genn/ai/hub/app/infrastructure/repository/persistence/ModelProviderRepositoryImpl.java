package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.ModelProviderAssembler;
import cn.genn.ai.hub.app.application.dto.response.ModelProviderDTO;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ModelProviderMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelProviderPO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 运单仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class ModelProviderRepositoryImpl extends ServiceImpl<ModelProviderMapper, ModelProviderPO> {

    @Resource
    private ModelProviderMapper providerMapper;
    @Resource
    private ModelProviderAssembler providerAssembler;

    public List<ModelProviderDTO> getActiveList() {
        QueryWrapper<ModelProviderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(ModelProviderPO::getIsActive, true)
        ;
        return providerAssembler.convert(providerMapper.selectList(queryWrapper));
    }

    public ModelProviderDTO getInfo(Long id) {
        return providerAssembler.convert(providerMapper.selectById(id));
    }
}
