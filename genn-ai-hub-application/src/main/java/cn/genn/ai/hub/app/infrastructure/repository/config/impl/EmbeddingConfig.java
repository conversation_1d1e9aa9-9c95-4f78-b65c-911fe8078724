package cn.genn.ai.hub.app.infrastructure.repository.config.impl;

import cn.genn.ai.hub.app.infrastructure.repository.config.ModelConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 索引模型 embedding
 */
@Getter
@Setter
@ToString
public class EmbeddingConfig implements ModelConfig {

    // 模型综合价格
    private BigDecimal charsPointsPrice;
    // 最大上下文长度
    private Integer maxContext;

    // 最大响应tokens长度
    private Integer defaultToken;

    // 归一处理：未对向量进行归一化处理
    private Boolean normalization;

    private Boolean isCustom;
    @Override
    public boolean validate() {
        return normalization != null;
    }
}
