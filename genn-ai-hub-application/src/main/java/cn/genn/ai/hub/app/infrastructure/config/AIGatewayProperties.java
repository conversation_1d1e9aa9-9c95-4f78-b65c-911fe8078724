package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AIGatewayProperties {

    /**
     * 智能体调用域名
     */
    private String invokeDomain = "http://localhost:8405";

    private String modeApi = "/cache/sync/model";

    private String agentApi = "/cache/sync/agent";

    private String sensitiveApi = "/cache/sync/sensitive";

    public String getModeURL() {
        return invokeDomain + modeApi;
    }


    public String getAgentURL() {
        return invokeDomain + agentApi;
    }

    public String getSensitiveURL() {
        return invokeDomain + sensitiveApi;
    }
}
