package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.DailyAnalysisEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * GovowOperationDrainagePO对象
 *
 * <AUTHOR>
 * @desc 格物运营-用户引流
 */
@Data
@TableName(value = "govow_operation_drainage", autoResultMap = true)
public class GovowOperationDrainagePO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 缓存键
     */
    @TableField("cache_key")
    private String cacheKey;

    /**
     * 问题简介
     */
    @TableField("question_short")
    private String questionShort;

    /**
     * 问题详情
     */
    @TableField("question_detail")
    private String questionDetail;

    /**
     * 部门
     */
    @TableField("department")
    private String department;

    /**
     * 其他信息
     */
    @TableField(value = "extra_info")
    private String extraInfo;

    /**
     * 飞书用户ID
     */
    @TableField("open_id")
    private String openId;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    @TableField("chat_id")
    private String chatId;

    /**
     * 执行类型
     */
    @TableField("source")
    private DailyAnalysisEnum source;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 头像
     */
    @TableField(value = "create_user_avatar", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserAvatar;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}
