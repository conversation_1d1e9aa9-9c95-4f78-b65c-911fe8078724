package cn.genn.ai.hub.app.infrastructure.repository.config;

import cn.genn.ai.hub.app.application.enums.ModelTypeEnum;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * 模型不同的配置，通过子类来定义
 *
 * <AUTHOR>
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
// @JsonTypeInfo 注解的作用，Jackson 多态
// 1. 序列化到时数据库时，增加 @class 属性。
// 2. 反序列化到内存对象时，通过 @class 属性，可以创建出正确的类型
public interface ModelConfig {

    boolean validate();
}
