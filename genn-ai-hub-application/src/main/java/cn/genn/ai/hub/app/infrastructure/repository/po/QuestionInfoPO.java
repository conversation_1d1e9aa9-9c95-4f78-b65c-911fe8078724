package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * QuestionInfoPO对象
 *
 * <AUTHOR>
 * @desc 问题记录表
 */
@Data
@TableName(value = "question_info", autoResultMap = true)
public class QuestionInfoPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 工作流id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 日期
     */
    @TableField("date")
    private LocalDate date;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 会话的关联id
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 一次问答关联id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 原始问题
     */
    @TableField("question")
    private String question;

    /**
     * 最终回答
     */
    @TableField("answer")
    private String answer;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户飞书openid
     */
    @TableField("open_id")
    private String openId;

    /**
     * 问题类型
     */
    @TableField("type")
    private String type;

    /**
     * 一级部门名称
     */
    @TableField("first_department")
    private String firstDepartment;

    /**
     * 最下级部门名称
     */
    @TableField("last_department")
    private String lastDepartment;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}

