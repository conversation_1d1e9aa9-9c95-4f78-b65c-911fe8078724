package cn.genn.ai.hub.app.infrastructure.external.feishu.handler;

import cn.genn.ai.hub.app.application.dto.feishu.CardTriggerDTO;
import cn.genn.ai.hub.app.application.dto.feishu.EventHandleDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FeishuCard;
import cn.genn.ai.hub.app.application.enums.feishu.ChatTypeEnum;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.EventCallbackEnum;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.FeishuEventParser;
import cn.genn.ai.hub.app.infrastructure.external.feishu.utils.FeishuCardUtils;
import cn.genn.ai.hub.app.infrastructure.external.feishu.utils.FeishuMessageSender;
import cn.genn.core.utils.jackson.JsonUtils;
import com.lark.oapi.Client;
import com.lark.oapi.service.im.v1.model.PatchMessageReq;
import com.lark.oapi.service.im.v1.model.PatchMessageReqBody;
import com.lark.oapi.service.im.v1.model.PatchMessageResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.genn.ai.hub.app.infrastructure.external.feishu.utils.FeishuCardUtils.*;

/**
 * <AUTHOR>
 * @description 卡片操作事件处理器
 * @date 2025-05-13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CardActionTriggerHandler implements FeishuCallbackHandler {

    private final FeishuMessageSender feishuMessageSender;

    private final FeishuClientService feishuClientService;

    private interface ActionHandler {
        void handle(EventHandleDTO command, CardTriggerDTO event, Map<String, Object> params);
    }

    private final Map<String, ActionHandler> actionHandlers = Map.of(
        "select_static", this::handleSelectStatic,
        "button", this::handleButton
    );

    @Override
    public void handle(EventHandleDTO command) {
        try {
            CardTriggerDTO event = FeishuEventParser.parseEvent(
                command.getEvent(),
                CardTriggerDTO.class
            );
            Map<String, Object> params = extractParams(event);
            String tag = event.getAction().getTag();

            actionHandlers.getOrDefault(tag, this::handleUnknown).handle(command, event, params);
            // 虚拟线程，延时5s更新卡片（回调方法内不允许更新卡片）
            Thread.ofVirtual().start(() -> {
                try {
                    Thread.sleep(5000);
                    disableCard(command, event, params);
                } catch (InterruptedException e) {
                    log.warn("Card disabling virtual thread interrupted | messageId:{}", event.getContext().getOpenMessageId(), e);
                } catch (Exception e) {
                    log.error("Card disable failed during delayed execution (virtual thread) | messageId:{} | params:{}", event.getContext().getOpenMessageId(), params, e);
                }
            });
        } catch (Exception e) {
            log.error("Card action handling failed | event: {}", JsonUtils.toJson(command.getEvent()), e);
        }
    }

    private void handleSelectStatic(EventHandleDTO command, CardTriggerDTO event, Map<String, Object> params) {
        String option = event.getAction().getOption();
        feishuMessageSender.handleTextMessage(
            command,
            ChatTypeEnum.of((String) params.get(KEY_CHAT_TYPE)),
            (String) params.get(KEY_APP_ID),
            option,
            (String) params.get(KEY_MESSAGE_ID),
            (String) params.get(KEY_CHAT_ID),
            (String) params.get(KEY_OPEN_ID));
    }

    private void handleButton(EventHandleDTO command, CardTriggerDTO event, Map<String, Object> params) {
        feishuMessageSender.handleTextMessage(
            command,
            ChatTypeEnum.of((String) params.get(KEY_CHAT_TYPE)),
            (String) params.get(KEY_APP_ID),
            JsonUtils.toJson(event.getAction().getFormValue()),
            (String) params.get(KEY_MESSAGE_ID),
            (String) params.get(KEY_CHAT_ID),
            (String) params.get(KEY_OPEN_ID));
    }

    private void handleUnknown(EventHandleDTO command, CardTriggerDTO event, Map<String, Object> params) {
        log.warn("Unknown card action tag: {} | params: {}", event.getAction().getTag(), params);
    }

    // 参数提取方法
    private Map<String, Object> extractParams(CardTriggerDTO event) {
        Map<String, Object> params = event.getAction().getValue();
        validateRequiredParams(params);
        return params;
    }

    private void validateRequiredParams(Map<String, Object> params) {
        List<String> requiredKeys = Arrays.asList(KEY_APP_ID, KEY_CHAT_ID, KEY_MESSAGE_ID);
        requiredKeys.forEach(key -> {
            if (!params.containsKey(key)) {
                throw new IllegalArgumentException("Missing required parameter: " + key);
            }
        });
    }

    private void disableCard(EventHandleDTO command, CardTriggerDTO event, Map<String, Object> params) {
        try {
            String cardJson = (String) params.get(KEY_CARD);
            FeishuCard card = JsonUtils.parse(cardJson, FeishuCard.class);
            FeishuCardUtils.disableCard(card, event);

            String disabledCardJson = JsonUtils.toJson(card);
            log.info("Disabling card | messageId:{} | card:{}", event.getContext().getOpenMessageId(), disabledCardJson);

            PatchMessageReq req = PatchMessageReq.newBuilder()
                .messageId(event.getContext().getOpenMessageId())
                .patchMessageReqBody(PatchMessageReqBody.newBuilder()
                    .content(disabledCardJson)
                    .build())
                .build();

            // 发起请求
            Client client = feishuClientService.getClient(command.getAppId());
            PatchMessageResp resp = client.im().v1().message().patch(req);
            log.info("Card disabled | messageId:{} | response:{}", event.getContext().getOpenMessageId(), JsonUtils.toJson(resp));

        } catch (Exception e) {
            log.error("Card disable failed | params:{}", params, e);
        }
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.CARD_ACTION_TRIGGER;
    }
}
