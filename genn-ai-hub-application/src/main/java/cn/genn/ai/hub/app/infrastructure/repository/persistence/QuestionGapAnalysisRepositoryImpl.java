package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.QuestionGapAnalysisAssembler;
import cn.genn.ai.hub.app.application.command.QuestionGapAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.question.QuestionGapAnalysisDTO;
import cn.genn.ai.hub.app.application.query.QuestionGapAnalysisQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionGapAnalysisMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionGapAnalysisPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 知识缺口分析统计表
 *
 * <AUTHOR>
 */
@Repository
@IgnoreTenant
@RequiredArgsConstructor
public class QuestionGapAnalysisRepositoryImpl extends ServiceImpl<QuestionGapAnalysisMapper, QuestionGapAnalysisPO> {


    private final QuestionGapAnalysisMapper gapAnalysisMapper;
    private final QuestionGapAnalysisAssembler gapAnalysisAssembler;

    public List<QuestionGapAnalysisDTO> infos(QuestionGapAnalysisQuery query) {
        LambdaQueryWrapper<QuestionGapAnalysisPO> wrapper = new QueryWrapper<QuestionGapAnalysisPO>()
            .lambda()
            .eq(QuestionGapAnalysisPO::getAppId, query.getAppId())
            .in(CollUtil.isNotEmpty(query.getChatIds()), QuestionGapAnalysisPO::getChatId, query.getChatIds())
            .eq(StringUtils.isNotEmpty(query.getType()), QuestionGapAnalysisPO::getType, query.getType())
            .in(CollUtil.isNotEmpty(query.getSources()), QuestionGapAnalysisPO::getSource, query.getSources())
            .orderByDesc(QuestionGapAnalysisPO::getId);
        return gapAnalysisAssembler.PO2DTO(gapAnalysisMapper.selectList(wrapper));
    }

    public Long add(QuestionGapAnalysisCommand command) {
        QuestionGapAnalysisPO po = gapAnalysisAssembler.convertPO(command);
        saveOrUpdate(po);
        return po.getId();
    }

    public List<QuestionGapAnalysisDTO> queryByQuestionIds(List<Long> questionIds) {
        if (CollUtil.isEmpty(questionIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<QuestionGapAnalysisPO> wrapper = new QueryWrapper<QuestionGapAnalysisPO>()
            .lambda()
            .in(QuestionGapAnalysisPO::getQuestionId, questionIds);
        return gapAnalysisAssembler.PO2DTO(gapAnalysisMapper.selectList(wrapper));
    }

    public List<String> queryTypes(String appId) {
        return Optional.ofNullable(gapAnalysisMapper.getAllTypes(appId)).orElse(Lists.newArrayList());
    }
}
