package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentChannelMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description 智能体渠道仓储层实现
 * @date 2025-04-25
 */
@Repository
public class AgentChannelRepositoryImpl extends ServiceImpl<AgentChannelMapper, AgentChannelPO> {

    @IgnoreTenant
    public Optional<AgentChannelPO> findById(Long id) {
        return Optional.ofNullable(baseMapper.selectById(id));
    }

    @IgnoreTenant
    public AgentChannelPO countByChannelId(Long channelId) {
        return baseMapper.queryByChannelId(channelId);
    }

    @IgnoreTenant
    public AgentChannelPO queryByChannelUnique(ChannelTypeEnum channelType, String channelUnique) {
        return baseMapper.queryByChannelUnique(channelType.getCode(), channelUnique);
    }
}
