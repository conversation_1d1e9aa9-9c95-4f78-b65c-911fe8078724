package cn.genn.ai.hub.app.infrastructure.repository.config.impl;

import cn.genn.ai.hub.app.infrastructure.repository.config.ModelConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 语音模型 LLM
 */
@Getter
@Setter
@ToString
public class LLMConfig implements ModelConfig {

    // 支持模型推理
    private Boolean reasoning;

    // 启用敏感校验
    private Boolean censor;

    // 最大上下文长度
    private Integer maxContext;

    // 模型综合价格
    private BigDecimal charsPointsPrice;

    // 模型输入价格
    private BigDecimal inputPrice;

    // 模型输出价格
    private BigDecimal outputPrice;

    // 最大响应tokens长度
    private Integer maxResponse;

    // 知识库最大引用长度
    private Integer quoteMaxToken;

    // 最大温度
    private Integer maxTemperature;

    // 是否展示top-P
    private Boolean showTopP;

    // 响应格式
    private Object responseFormatList;

    // 展示停止序列参数
    private Boolean showStopSign;

    // 支持图片识别
    private Boolean vision;

    // 支持工具调用
    private Boolean toolChoice;

    // 支持函数调用
    private Boolean functionCall;

    // 默认提示词
    private String defaultSystemChatPrompt;

    // 用于知识库文件处理
    private Boolean datasetProcess;

    // 用于问题分类
    private Boolean usedInClassify;

    // 自定义问题分类提示词
    private String customCQPrompt;

    // 用于文本提取
    private Boolean usedInExtractFields;

    // 自定义内容提取提示词
    private String customExtractPrompt;

    // 用于工具调用节点
    private Boolean usedInToolCall;

    private Boolean isCustom;

    @Override
    public boolean validate() {
        return reasoning != null
//            && showStopSign != null
//            && vision != null
//            && toolChoice != null
//            && functionCall != null
//            && datasetProcess != null
//            && usedInClassify != null
//            && usedInExtractFields != null
//            && usedInToolCall != null
//            && showTopP != null
            ;
    }
}
