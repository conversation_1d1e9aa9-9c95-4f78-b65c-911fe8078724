package cn.genn.ai.hub.app.infrastructure.converter;

import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallAnalysisInfo;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallInfo;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallItem;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallAnalysisPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallItemPO;
import cn.genn.core.model.converter.POConverter;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RTCPitfallConverter extends POConverter<RTCPitfallInfo, RTCPitfallInfoPO> {

    RTCPitfallConverter INSTANCE = Mappers.getMapper(RTCPitfallConverter.class);

    List<RTCPitfallItem> toRTCPitfallItem(List<RTCPitfallItemPO> rtcPitfallItemPOs);

    List<RTCPitfallItemPO> toRTCPitfallItemPO(List<RTCPitfallItem> items);

    RTCPitfallAnalysisPO toRTCPitfallAnalysisPO(RTCPitfallAnalysisInfo rtcPitfallAnalysisInfo);
}
