package cn.genn.ai.hub.app.infrastructure.external.feishu.event;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 飞书V2版本事件
 * @date 2025-04-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FeishuV2Event extends FeishuBaseEvent {

    private String schema; // 固定 "2.0"
    private JsonNode header; // header 对象

    // 从 header 中提取事件类型
    public String getEventType() {
        return header != null ? header.path("event_type").asText() : null;
    }

    @Override
    public String getAppId() {
        return header != null ? header.path("app_id").asText() : null;
    }

    @Override
    public String getTenantKey() {
        return header != null ? header.path("tenant_key").asText() : null;
    }

    @Override
    public String getEventId() {
        return header != null ? header.path("event_id").asText() : null;
    }

    @Override
    public String getToken() {
        return header.path("token").asText();
    }
}
