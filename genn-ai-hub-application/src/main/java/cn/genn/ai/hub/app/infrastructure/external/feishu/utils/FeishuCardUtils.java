package cn.genn.ai.hub.app.infrastructure.external.feishu.utils;

import cn.genn.ai.hub.app.application.dto.AgentCompleteRespDTO;
import cn.genn.ai.hub.app.application.dto.feishu.CardTriggerDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FeishuCard;
import cn.genn.ai.hub.app.application.enums.feishu.ChatTypeEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 飞书卡片工具类
 * @date 2025-05-14
 */
@Slf4j
public class FeishuCardUtils {

    // 常量定义
    public static final String KEY_APP_ID = "appId";
    public static final String KEY_CHAT_ID = "chatId";
    public static final String KEY_MESSAGE_ID = "messageId";
    public static final String KEY_OPEN_ID = "openId";
    public static final String KEY_CHAT_TYPE = "chatType";
    public static final String KEY_CARD = "card";
    private static final String ACTION_TAG_SELECT_STATIC = "select_static"; // 静态选择器的tag
    private static final String ACTION_TAG_BUTTON = "button"; // 按钮的tag

    public static void disableCard(FeishuCard card, CardTriggerDTO event) {
        if (card == null || card.getBody() == null || card.getBody().getElements() == null || event == null || event.getAction() == null) {
            log.warn("无法禁用卡片：卡片对象、body、elements、事件或事件action为空。");
            return;
        }

        CardTriggerDTO.Action action = event.getAction();
        String eventActionTag = action.getTag();

        // 从事件中提取的值
        String selectedOptionForSelectStatic = null; // 用于静态选择器
        Map<String, Object> formValuesFromEvent = null; // 用于表单提交

        // 1. 根据事件类型，准备要设置的值
        if (ACTION_TAG_SELECT_STATIC.equals(eventActionTag)) {
            selectedOptionForSelectStatic = action.getOption();
            log.debug("事件类型：select_static。用户选择的选项值：{}", selectedOptionForSelectStatic);
        } else if (ACTION_TAG_BUTTON.equals(eventActionTag)) {
            // 如果是按钮点击，检查是否为表单提交（通过 action.formValue 是否有值来判断）
            if (action.getFormValue() != null && !action.getFormValue().isEmpty()) {
                formValuesFromEvent = action.getFormValue();
                log.debug("事件类型：button（判断为表单提交，因为 formValue 不为空）。表单提交数据：{}", formValuesFromEvent);
            }
        }

        // 2. 遍历卡片body中的顶层元素，并进行处理
        if (card.getBody().getElements() != null) {
            for (FeishuCard.Element topLevelElement : card.getBody().getElements()) {
                processElementAndChildren(topLevelElement, selectedOptionForSelectStatic, formValuesFromEvent);
            }
        }

        log.info("卡片元素的值设置与禁用处理完成。消息ID：{}", event.getContext().getOpenMessageId());
    }

    // 卡片转换策略
    public static FeishuCard convert2Card(AgentCompleteRespDTO.InteractiveContent interactiveContent,
                                          String appId, String chatId, String messageId,
                                          String openId, ChatTypeEnum chatType) {
        if (interactiveContent == null) return null;

        FeishuCard card = switch (interactiveContent.getType()) {
            case "userInput" -> createUserInputCard(interactiveContent, appId, chatId, messageId, openId, chatType);
            case "userSelect" -> createUserSelectCard(interactiveContent, appId, chatId, messageId, openId, chatType);
            default -> {
                log.error("Unsupported interactive type: {} for messageId {}", interactiveContent.getType(), messageId);
                throw new IllegalArgumentException("Unsupported interactive type: " + interactiveContent.getType());
            }
        };
        log.info("Converted card: {}", JsonUtils.toJson(card));
        return card;
    }

    private static FeishuCard createUserInputCard(AgentCompleteRespDTO.InteractiveContent content,
                                                  String appId, String chatId, String messageId,
                                                  String openId, ChatTypeEnum chatType) {
        FeishuCard card = new FeishuCard();
        FeishuCard.Body body = new FeishuCard.Body();

        List<FeishuCard.Element> elements = content.getParams().getInputForm().stream()
            .flatMap(inputForm -> {
                switch (inputForm.getType()) {
                    case "input":
                    case "numberInput":
                        FeishuCard.Input input = new FeishuCard.Input();
                        input.setRequired(inputForm.isRequired());
                        if (inputForm.getMaxLength() != null) {
                            input.setMaxLength(inputForm.getMaxLength());
                        }
                        if (inputForm.getDefaultValue() != null) {
                            input.setDefaultValue(inputForm.getDefaultValue());
                        }
                        input.setName(inputForm.getKey());
                        input.setLabel(createLabelElement(inputForm.getLabel()));
                        input.setPlaceholder(createPlaceholderElement(inputForm.getDescription()));
                        return Stream.of(input);
                    case "select":
                        List<FeishuCard.Element> selectElements = new ArrayList<>();
                        FeishuCard.Markdown markdown = new FeishuCard.Markdown();
                        markdown.setContent(inputForm.getKey());
                        selectElements.add(markdown);

                        FeishuCard.SelectStatic select = new FeishuCard.SelectStatic();
                        select.setName(inputForm.getKey());
                        if (inputForm.getDefaultValue() != null) {
                            select.setInitialOption(inputForm.getDefaultValue());
                        }
                        select.setPlaceholder(createPlaceholderElement(inputForm.getDescription()));
                        select.setOptions(inputForm.getList().stream()
                            .map(option -> {
                                FeishuCard.Option opt = new FeishuCard.Option();
                                opt.setText(createTextElement(option.getLabel()));
                                opt.setValue(option.getValue());
                                return opt;
                            })
                            .collect(Collectors.toList()));

                        selectElements.add(select);
                        return selectElements.stream();
                    default:
                        return Stream.empty();
                }
            }).filter(Objects::nonNull) // 过滤掉可能的 null (虽然 flatMap 返回 Stream.empty() 更佳)
            .collect(Collectors.toList());

        elements.add(createSubmitButton());

        FeishuCard.Form form = new FeishuCard.Form();
        form.setElements(elements);
        body.setElements(Collections.singletonList(form));
        card.setBody(body);

        FeishuCard.Button submitButton = findSubmitButton(card);

        if (submitButton != null) {
            FeishuCard.Behavior callbackBehavior = createCallbackBehavior(appId, chatId, messageId, openId, chatType, card);
            submitButton.setBehaviors(Collections.singletonList(callbackBehavior));
        }
        return card;
    }

    private static FeishuCard createUserSelectCard(AgentCompleteRespDTO.InteractiveContent content,
                                                   String appId, String chatId, String messageId,
                                                   String openId, ChatTypeEnum chatType) {
        FeishuCard card = new FeishuCard();
        FeishuCard.Body body = new FeishuCard.Body();

        FeishuCard.Markdown markdownElement = createMarkdownElement(content.getParams().getDescription());

        FeishuCard.SelectStatic select = new FeishuCard.SelectStatic();

        select.setOptions(content.getParams().getUserSelectOptions().stream()
            .map(option -> {
                FeishuCard.Option opt = new FeishuCard.Option();
                opt.setText(createTextElement(option.getValue()));
                opt.setValue(option.getValue());
                return opt;
            })
            .collect(Collectors.toList()));

        List<FeishuCard.Element> elements = new ArrayList<>();
        elements.add(markdownElement);
        elements.add(select);
        body.setElements(elements);
        card.setBody(body);
        select.setBehaviors(Collections.singletonList(createCallbackBehavior(appId, chatId, messageId, openId, chatType, card)));
        return card;
    }

    private static FeishuCard.Button findSubmitButtonRecursive(List<FeishuCard.Element> elements) {
        if (elements == null || elements.isEmpty()) {
            return null;
        }

        for (FeishuCard.Element element : elements) {
            if (element == null) {
                continue;
            }

            // 1. Check if the current element is the target button
            if (element instanceof FeishuCard.Button button && "submit".equals(button.getFormActionType())) {
                return button;
            }

            // 2. If it's a container, recursively search its children
            List<FeishuCard.Element> childrenToSearch = null;
            if (element instanceof FeishuCard.Form form) {
                childrenToSearch = form.getElements();
            } else if (element instanceof FeishuCard.ColumnSet columnSet) {
                if (columnSet.getColumns() != null) {
                    // Need to collect elements from all columns
                    childrenToSearch = new ArrayList<>();
                    for (FeishuCard.Column column : columnSet.getColumns()) {
                        if (column != null && column.getElements() != null) {
                            childrenToSearch.addAll(column.getElements());
                        }
                    }
                }
            }

            if (childrenToSearch != null) {
                FeishuCard.Button foundInChild = findSubmitButtonRecursive(childrenToSearch);
                if (foundInChild != null) {
                    return foundInChild;
                }
            }
        }
        return null;
    }

    private static FeishuCard.Button findSubmitButton(FeishuCard card) {
        if (card == null || card.getBody() == null || card.getBody().getElements() == null) {
            return null;
        }
        return findSubmitButtonRecursive(card.getBody().getElements());
    }

    // 公共组件创建方法
    private static FeishuCard.Text createTextElement(String content) {
        FeishuCard.Text text = new FeishuCard.Text();
        text.setTag("plain_text");
        text.setContent(content);
        return text;
    }

    private static FeishuCard.Markdown createMarkdownElement(String content) {
        FeishuCard.Markdown text = new FeishuCard.Markdown();
        text.setContent(content);
        return text;
    }

    private static FeishuCard.Placeholder createPlaceholderElement(String content) {
        FeishuCard.Placeholder text = new FeishuCard.Placeholder();
        text.setTag("plain_text");
        text.setContent(content);
        return text;
    }

    private static FeishuCard.Label createLabelElement(String content) {
        FeishuCard.Label text = new FeishuCard.Label();
        text.setTag("plain_text");
        text.setContent(content);
        return text;
    }

    private static FeishuCard.Button createSubmitButton() {
        FeishuCard.Button button = new FeishuCard.Button();
        button.setName("submitBtn");
        button.setText(createTextElement("提交"));
        button.setType("primary");
        button.setFormActionType("submit");
        return button;
    }

    private static FeishuCard.Behavior createCallbackBehavior(String appId, String chatId, String messageId,
                                                              String openId, ChatTypeEnum chatType, FeishuCard card) {
        FeishuCard.Behavior behavior = new FeishuCard.Behavior();
        behavior.setType("callback");

        Map<String, Object> valueMap = new LinkedHashMap<>();
        valueMap.put(KEY_APP_ID, appId);
        valueMap.put(KEY_CHAT_ID, chatId);
        valueMap.put(KEY_MESSAGE_ID, messageId);
        valueMap.put(KEY_OPEN_ID, openId);
        valueMap.put(KEY_CHAT_TYPE, chatType.getType());
        valueMap.put(KEY_CARD, JsonUtils.toJson(card));

        behavior.setValue(valueMap);
        return behavior;
    }

    /**
     * 递归（或迭代）处理卡片元素及其子元素。
     *
     * @param element                       当前待处理的元素。
     * @param selectedOptionForSelectStatic 如果是 select_static 事件，用户选择的选项值。
     * @param formValuesFromEvent           如果是表单提交事件，用户提交的表单数据。
     */
    private static void processElementAndChildren(FeishuCard.Element element,
                                                  String selectedOptionForSelectStatic,
                                                  Map<String, Object> formValuesFromEvent) {
        switch (element) {
            case null -> {
                return;
            }


            case FeishuCard.Markdown ignored -> {
                return;
            }

            // --- 特殊处理：Form 容器 ---
            case FeishuCard.Form formElement -> {
                log.debug("正在处理 Form 容器 (tag: {})。Form 容器本身不被禁用。", formElement.getTag());
                if (formElement.getElements() != null) {
                    for (FeishuCard.Element childInForm : formElement.getElements()) {
                        // 将 formValuesFromEvent 传递给 Form 内部的子元素处理逻辑
                        processElementAndChildren(childInForm, selectedOptionForSelectStatic, formValuesFromEvent);
                    }
                }
                return;
            }


            // --- 对于非 Form 容器的元素，或 Form 容器内部的元素 ---

            // A. 设置元素的值（如果适用）
            case FeishuCard.SelectStatic selectElement -> {
                // 情况1: 事件是 select_static，直接设置当前选择器的值
                if (selectedOptionForSelectStatic != null) {
                    // 根据您的模型，可能是 setInitialOption 或 setValue
                    selectElement.setInitialOption(selectedOptionForSelectStatic); // 或者 selectElement.setValue(selectedOptionForSelectStatic);
                }
                // 情况2: 事件是表单提交，并且这个选择器是表单的一部分
                if (formValuesFromEvent != null && selectElement.getName() != null && formValuesFromEvent.containsKey(selectElement.getName())) {
                    Object valueFromForm = formValuesFromEvent.get(selectElement.getName());
                    if (valueFromForm != null) {
                        selectElement.setInitialOption(String.valueOf(valueFromForm));
                    }
                }
            }
            case FeishuCard.Input inputElement -> {
                inputElement.setRequired(false);
                // 情况: 事件是表单提交，设置输入框的值
                if (formValuesFromEvent != null && inputElement.getName() != null && formValuesFromEvent.containsKey(inputElement.getName())) {
                    Object valueFromForm = formValuesFromEvent.get(inputElement.getName());
                    if (valueFromForm != null) {
                        inputElement.setDefaultValue(String.valueOf(valueFromForm));
                    }
                }
            }
            default -> {
            }
        }


        // B. 禁用当前元素
        element.setDisabled(true);
    }
}
