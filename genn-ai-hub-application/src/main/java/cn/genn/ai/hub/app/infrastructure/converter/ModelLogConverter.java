package cn.genn.ai.hub.app.infrastructure.converter;

import cn.genn.ai.hub.app.infrastructure.repository.po.ModelLogPO;
import cn.genn.spring.boot.starter.ai.component.log.ModelLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModelLogConverter {

    ModelLogConverter INSTANCE = Mappers.getMapper(ModelLogConverter.class);

    ModelLogPO toModelLogPO(ModelLog modelLog);

    ModelLogPO MQLogToPO(cn.genn.ai.hub.app.application.listener.event.ModelLog log);
}

