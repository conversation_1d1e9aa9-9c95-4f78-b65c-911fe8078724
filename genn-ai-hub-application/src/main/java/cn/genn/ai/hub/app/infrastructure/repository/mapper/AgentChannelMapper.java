package cn.genn.ai.hub.app.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
public interface AgentChannelMapper extends BaseMapper<AgentChannelPO> {

    @Select("SELECT ac.* FROM agent_channel ac \n" +
        "        left join tenant_channel_ref tcr on ac.channel_ref_id = tcr.id\n" +
        "        left join channel_base cb on tcr.channel_id = cb.id\n" +
        "        WHERE tcr.channel_id = #{channelId}")
    AgentChannelPO queryByChannelId(@Param("channelId") Long channelId);

    @Select("SELECT ac.* FROM agent_channel ac\n" +
        "       left join tenant_channel_ref tcr on ac.channel_ref_id = tcr.id\n" +
        "        left join channel_base cb on tcr.channel_id = cb.id\n" +
        "        WHERE cb.unique_identifier = #{channelUnique} AND cb.channel_type = #{channelType}")
    AgentChannelPO queryByChannelUnique(@Param("channelType") String channelType,
        @Param("channelUnique") String channelUnique);
}
