package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * TenantChannelRefPO对象
 *
 * <AUTHOR>
 * @desc 租户空间渠道关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "tenant_channel_ref", autoResultMap = true)
public class TenantChannelRefPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 团队id，如果为空，代表是个人空间创建
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * 渠道类型(如feishu/wechat)
     */
    @TableField("channel_type")
    private ChannelTypeEnum channelType;

    /**
     * 渠道id
     */
    @TableField("channel_id")
    private Long channelId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Boolean deleted;

}

