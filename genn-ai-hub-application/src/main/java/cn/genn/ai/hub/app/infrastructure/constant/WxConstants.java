package cn.genn.ai.hub.app.infrastructure.constant;

/**
 * <AUTHOR>
 * @description 微信相关常量定义
 * @date 2025-04-07
 */
public class WxConstants {

    /**
     * 统一的缓存前缀
     */
    public static final String CACHE_PRE = "GENN:AI:HUB";

    /**
     * 微信公众号access_token过期时间
     */
    public static final long WX_MP_ACCESS_TOKEN_EXPIRE_TIME = 60 * 100L;

    /**
     * 微信相关key
     */
    public static String WX_KEY = CACHE_PRE + ":WX";

    /**
     * 微信公众号相关key
     */
    public static String WX_MP_KEY = WX_KEY + ":MP";

    /**
     * 微信公众号access_token
     */
    public static String WX_MP_ACCESS_TOKEN_KEY = WX_MP_KEY + ":ACCESS_TOKEN:";

    /**
     * 微信公众号消息重复性校验key
     */
    public static String WX_MP_MESSAGE_KEY = WX_MP_KEY + ":MESSAGE:";

    /**
     * 微信公众号消息重复性校验过期时间
     */
    public static Integer WX_MP_MESSAGE_EXPIRE_TIME = 60 * 30;

    public static String WX_DOMAIN = "https://api.weixin.qq.com";

    /**
     * 微信公众号access_token接口url
     */
    public static String WX_GET_ACCESS_TOKEN_URL = WX_DOMAIN + "/cgi-bin/token";

    /**
     * 微信公众号发送客服消息接口url
     */
    public static String WX_SEND_CUSTOM_MESSAGE_URL = WX_DOMAIN + "/cgi-bin/message/custom/send";

}
