package cn.genn.ai.hub.app.infrastructure.exception;

import cn.genn.ai.hub.core.exception.ExceptionBizCode;
import cn.genn.core.exception.MessageCodeWrap;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 智能体调用异常码
 * @date 2025-05-14
 */
@Getter
@AllArgsConstructor
public enum AgentInvokeErrorCode implements MessageCodeWrap {

    INIT_CHAT("201", "智能体初始化失败: {0}"),
    COMPLETE("202", "智能体完成失败: {0}"),
    CLEAR_CHAT("203", "会话清理失败: {0}"),
    FAIL("500", "智能体调用失败"),
    ;

    private final String code;
    private final String description;

    @Override
    public String getCode() {
        return ExceptionBizCode.APP.getBizCode();
    }
}
