package cn.genn.ai.hub.app.infrastructure.external.feishu.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-04-09
 */
@Data
public class FeishuP2pChatCreateEvent {

    @JsonProperty("app_id")
    private String appId;

    @JsonProperty("chat_id")
    private String chatId;

    private Operator operator;

    @JsonProperty("tenant_key")
    private String tenantKey;

    private String type; // "p2p_chat_create"

    private User user;

    @Data
    public static class Operator {
        @JsonProperty("open_id")
        private String openId;

        @JsonProperty("user_id")
        private String userId;
    }

    @Data
    public static class User {
        private String name;

        @JsonProperty("open_id")
        private String openId;

        @JsonProperty("user_id")
        private String userId;
    }
}
