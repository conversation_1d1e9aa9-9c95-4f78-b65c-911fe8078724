package cn.genn.ai.hub.app.infrastructure.external.feishu.handler;

import cn.genn.ai.hub.app.infrastructure.external.feishu.event.EventCallbackEnum;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 回调处理器配置
 * @date 2024-12-25
 */
@Configuration
public class CallbackHandlerConfig {

    @Resource
    private List<FeishuCallbackHandler> feishuCallbackHandlers;

    @Bean
    public Map<EventCallbackEnum, FeishuCallbackHandler> callbackHandlerMap() {
        return feishuCallbackHandlers.stream().collect(
                java.util.stream.Collectors.toMap(FeishuCallbackHandler::event, java.util.function.Function.identity()));
    }
}
