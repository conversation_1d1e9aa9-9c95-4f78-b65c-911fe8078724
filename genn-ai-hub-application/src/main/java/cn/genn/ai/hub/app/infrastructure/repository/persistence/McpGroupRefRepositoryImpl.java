package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroupRef;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpGroupRefRepository;
import cn.genn.ai.hub.app.infrastructure.converter.McpGroupRefConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.McpGroupRefMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpGroupRefPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MCP分组关联仓储实现
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class McpGroupRefRepositoryImpl extends ServiceImpl<McpGroupRefMapper, McpGroupRefPO> implements IMcpGroupRefRepository {

    private final McpGroupRefConverter mcpGroupRefConverter;

    @Override
    public boolean save(McpGroupRef mcpGroupRef) {
        McpGroupRefPO mcpGroupRefPO = mcpGroupRefConverter.toMcpGroupRefPO(mcpGroupRef);
        boolean result = super.save(mcpGroupRefPO);
        if (result) {
            mcpGroupRef.setId(mcpGroupRefPO.getId());
        }
        return result;
    }

    @Override
    public boolean saveBatch(List<McpGroupRef> mcpGroupRefs) {
        List<McpGroupRefPO> mcpGroupRefPOs = mcpGroupRefConverter.toMcpGroupRefPOList(mcpGroupRefs);
        return super.saveBatch(mcpGroupRefPOs);
    }

    @Override
    public McpGroupRef getById(Long id) {
        McpGroupRefPO mcpGroupRefPO = super.getById(id);
        return mcpGroupRefPO != null ? mcpGroupRefConverter.toMcpGroupRef(mcpGroupRefPO) : null;
    }

    @Override
    public List<McpGroupRef> listByGroupId(Long groupId) {
        LambdaQueryWrapper<McpGroupRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McpGroupRefPO::getGroupId, groupId);
        List<McpGroupRefPO> mcpGroupRefPOs = super.list(queryWrapper);
        return mcpGroupRefConverter.toMcpGroupRefList(mcpGroupRefPOs);
    }

    @Override
    public Map<Long, List<McpGroupRef>> listByGroupIds(Collection<Long> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            return new HashMap<>();
        }

        // 创建查询条件
        LambdaQueryWrapper<McpGroupRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(McpGroupRefPO::getGroupId, groupIds);

        // 执行查询
        List<McpGroupRefPO> mcpGroupRefPOs = super.list(queryWrapper);
        if (mcpGroupRefPOs == null || mcpGroupRefPOs.isEmpty()) {
            return groupIds.stream().collect(Collectors.toMap(groupId -> groupId, groupId -> new ArrayList<>()));
        }

        // 转换为领域实体
        List<McpGroupRef> mcpGroupRefs = mcpGroupRefConverter.toMcpGroupRefList(mcpGroupRefPOs);

        // 按分组ID分组
        Map<Long, List<McpGroupRef>> result = mcpGroupRefs.stream()
                .collect(Collectors.groupingBy(McpGroupRef::getGroupId));

        // 确保每个分组ID都有一个列表，即使是空列表
        for (Long groupId : groupIds) {
            if (!result.containsKey(groupId)) {
                result.put(groupId, new ArrayList<>());
            }
        }

        return result;
    }

    @Override
    public List<McpGroupRef> listByMcpId(Long mcpId) {
        LambdaQueryWrapper<McpGroupRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McpGroupRefPO::getMcpId, mcpId);
        List<McpGroupRefPO> mcpGroupRefPOs = super.list(queryWrapper);
        return mcpGroupRefConverter.toMcpGroupRefList(mcpGroupRefPOs);
    }

    @Override
    public Map<Long, List<McpGroupRef>> listByMcpIds(Collection<Long> mcpIds) {
        if (mcpIds == null || mcpIds.isEmpty()) {
            return new HashMap<>();
        }

        // 创建查询条件
        LambdaQueryWrapper<McpGroupRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(McpGroupRefPO::getMcpId, mcpIds);

        // 执行查询
        List<McpGroupRefPO> mcpGroupRefPOs = super.list(queryWrapper);
        if (mcpGroupRefPOs == null || mcpGroupRefPOs.isEmpty()) {
            return mcpIds.stream().collect(Collectors.toMap(mcpId -> mcpId, mcpId -> new ArrayList<>()));
        }

        // 转换为领域实体
        List<McpGroupRef> mcpGroupRefs = mcpGroupRefConverter.toMcpGroupRefList(mcpGroupRefPOs);

        // 按McpId分组
        Map<Long, List<McpGroupRef>> result = mcpGroupRefs.stream()
                .collect(Collectors.groupingBy(McpGroupRef::getMcpId));

        // 确保每个mcpId都有一个列表，即使是空列表
        for (Long mcpId : mcpIds) {
            if (!result.containsKey(mcpId)) {
                result.put(mcpId, new ArrayList<>());
            }
        }

        return result;
    }


    @Override
    public boolean updateById(McpGroupRef mcpGroupRef) {
        McpGroupRefPO mcpGroupRefPO = mcpGroupRefConverter.toMcpGroupRefPO(mcpGroupRef);
        return super.updateById(mcpGroupRefPO);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public boolean removeByGroupId(Long groupId) {
        LambdaQueryWrapper<McpGroupRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McpGroupRefPO::getGroupId, groupId);
        return super.remove(queryWrapper);
    }

    @Override
    public boolean removeByMcpId(Long mcpId) {
        LambdaQueryWrapper<McpGroupRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McpGroupRefPO::getMcpId, mcpId);
        return super.remove(queryWrapper);
    }
}
