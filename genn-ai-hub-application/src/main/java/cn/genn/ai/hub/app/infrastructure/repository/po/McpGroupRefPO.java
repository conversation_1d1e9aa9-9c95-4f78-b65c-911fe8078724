package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * MCP分组关联PO对象
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "mcp_group_ref", autoResultMap = true)
public class McpGroupRefPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 分组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * MCP ID
     */
    @TableField("mcp_id")
    private Long mcpId;
}
