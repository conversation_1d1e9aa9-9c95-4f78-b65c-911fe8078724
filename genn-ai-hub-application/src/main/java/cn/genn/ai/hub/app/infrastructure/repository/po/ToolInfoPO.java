package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.ToolSourceEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * ToolInfoPO对象
 *
 * <AUTHOR>
 * @desc 通用工具
 */
@Data
@TableName(value = "tool_info", autoResultMap = true)
public class ToolInfoPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 工具key
     */
    @TableField("tool_key")
    private String toolKey;

    /**
     * 工具来源，web：WEB-前端工具, server：SERVER-后端工具
     */
    @TableField("tool_source")
    private ToolSourceEnum toolSource;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 描述
     */
    @TableField("intro")
    private String intro;

    /**
     * 排序,值越大越靠前
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

