package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.QaPairStatusEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.List;


/**
 * KbRepoQaPairTempPO对象
 *
 * <AUTHOR>
 * @desc 知识库问答对临时表
 */
@Data
@TableName(value = "kb_repo_qa_pair_temp", autoResultMap = true)
public class KbRepoQaPairTempPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 知识库ID，标识集合所属的数据库（关联 repo 表）
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 问答对唯一标识
     */
    @TableField("qa_pair_key")
    private String qaPairKey;

    /**
     * 问题
     */
    @TableField("question")
    private String question;

    /**
     * 相似问题
     */
    @TableField(value = "similar_questions", typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<String> similarQuestions;

    /**
     * 回答
     */
    @TableField("answer")
    private String answer;

    /**
     * 问答对状态,0:NOT_CHECK-未校验,1:NOT_ACCEPT-未采纳,2:ACCEPTED-已采纳
     */
    @TableField("qa_pair_status")
    private QaPairStatusEnum qaPairStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

