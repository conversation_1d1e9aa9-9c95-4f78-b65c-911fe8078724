package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.enums.PromptTypeEnum;
import cn.genn.ai.hub.app.application.query.PromptQuery;
import cn.genn.ai.hub.app.domain.prompt.model.entity.Prompt;
import cn.genn.ai.hub.app.domain.prompt.model.entity.PromptVersion;
import cn.genn.ai.hub.app.domain.prompt.repository.PromptRepository;
import cn.genn.ai.hub.app.infrastructure.converter.PromptConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.PromptMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.PromptVersionMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptVersionPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 提示词仓储层实现
 * @date 2025-05-14
 */
@Repository
@RequiredArgsConstructor
public class PromptRepositoryImpl extends ServiceImpl<PromptMapper, PromptPO> implements PromptRepository {

    private final PromptConverter promptConverter;

    private final PromptVersionMapper promptVersionMapper;

    @Override
    public void save(Prompt prompt) {
        PromptPO promptPO = promptConverter.toPO(prompt);

        if (promptPO.getId() == null) {
            // 新建 Prompt
            this.getBaseMapper().insert(promptPO); // promptPO.id 会被设置
            prompt.setId(promptPO.getId()); // 将生成的ID回写到领域对象
        } else {
            // 更新 Prompt
            this.getBaseMapper().updateById(promptPO);
        }

        // 更新领域对象中 PromptVersion 的 promptId（对于新建的 Prompt）
        // 确保所有版本都有正确的 promptId 和 tenantId
        prompt.getAllVersions().forEach(versionDO -> {
            versionDO.setPromptId(prompt.getId());
            versionDO.setTenantId(prompt.getTenantId());
        });

        // 处理 PromptVersion: 同步数据库中的版本与领域对象中的版本
        // 1. 获取数据库中当前 Prompt 的所有版本
        List<PromptVersionPO> dbVersions = prompt.getId() == null ? Collections.emptyList() :
            promptVersionMapper.selectList(
                new LambdaQueryWrapper<PromptVersionPO>().eq(PromptVersionPO::getPromptId, prompt.getId())
            );

        List<PromptVersion> domainVersions = prompt.getAllVersions(); // 当前领域对象中的版本

        // 2. 更新或插入领域对象中的版本
        for (PromptVersion versionDO : domainVersions) {
            PromptVersionPO versionPO = promptConverter.versionToPO(versionDO);
            versionPO.setPromptId(prompt.getId()); // 确保关联
            versionPO.setTenantId(prompt.getTenantId()); // 确保一致

            Optional<PromptVersionPO> existingDbVersion = dbVersions.stream()
                .filter(dbv -> dbv.getVersion().equals(versionPO.getVersion()))
                .findFirst();

            if (existingDbVersion.isPresent()) { // 版本已存在于数据库
                versionPO.setId(existingDbVersion.get().getId()); // 确保ID正确以进行更新
                promptVersionMapper.updateById(versionPO);
                versionDO.setId(versionPO.getId()); // 回写ID到领域对象（如果之前没有）
            } else { // 新版本
                promptVersionMapper.insert(versionPO);
                versionDO.setId(versionPO.getId()); // 回写ID
            }
        }
    }

    @Override
    public Optional<Prompt> findById(Long id) {
        PromptPO promptPO = this.getBaseMapper().selectById(id);
        if (promptPO == null) {
            return Optional.empty();
        }
        // 加载关联的 PromptVersionPO
        List<PromptVersionPO> versionPOs = promptVersionMapper.selectList(
            new LambdaQueryWrapper<PromptVersionPO>().eq(PromptVersionPO::getPromptId, id)
        );
        // 转换为领域对象
        Prompt prompt = promptConverter.toEntity(promptPO);
        if (prompt != null) {
            List<PromptVersion> versions = promptConverter.versionToEntityList(versionPOs);
            prompt.setPromptVersions(versions); // 使用 Prompt 实体中的 setter
        }
        return Optional.ofNullable(prompt);
    }

    @Override
    public Optional<Prompt> findByTeamIdAndTitle(Long teamId, String title) {
        LambdaQueryWrapper<PromptPO> queryWrapper = new LambdaQueryWrapper<PromptPO>()
            .eq(PromptPO::getTitle, title);
        if (teamId != null) {
            queryWrapper.eq(PromptPO::getTeamId, teamId);
        } else {
            queryWrapper.isNull(PromptPO::getTeamId);
        }
        PromptPO promptPO = this.getBaseMapper().selectOne(queryWrapper);
        // 这里不需要加载版本，因为只是检查唯一性
        return Optional.ofNullable(promptConverter.toEntity(promptPO));
    }

    @Override
    public Page<PromptPO> findPage(PromptQuery promptQuery) {
        Page<PromptPO> page = new Page<>(promptQuery.getPageNo(), promptQuery.getPageSize());
        LambdaQueryWrapper<PromptPO> wrapper = new LambdaQueryWrapper<>();

        // 通用查询条件：标题
        wrapper.like(promptQuery.getTitle() != null, PromptPO::getTitle, promptQuery.getTitle());

        // 根据 promptType 是否传入，构造不同的查询逻辑
        if (promptQuery.getPromptType() == null) {
            // 情况1: promptType 未传入，查询系统提示词 或 用户提示词
            wrapper.and(mainOr -> mainOr
                // 条件A: 系统提示词 (promptType = SYSTEM)，不进行 teamId 或 createUserId 过滤
                .eq(PromptPO::getPromptType, PromptTypeEnum.SYSTEM) // 假设 PromptPO 有 getPromptType 方法，且 PromptTypeEnum.SYSTEM 存在
                // 条件B: 用户提示词 (promptType = USER)，并应用 teamId 或 createUserId 过滤
                .or(userPromptConditions -> {
                    userPromptConditions.eq(PromptPO::getPromptType, PromptTypeEnum.USER); // 假设 PromptTypeEnum.USER 存在
                    userPromptConditions.eq(PromptPO::getTenantId, CurrentUserHolder.getTenantId());
                    if (promptQuery.getTeamId() != null) {
                        // 团队空间的提示词
                        userPromptConditions.eq(PromptPO::getTeamId, promptQuery.getTeamId());
                    } else {
                        // 个人空间的提示词 (teamId 为 null 且创建者为当前用户)
                        userPromptConditions.isNull(PromptPO::getTeamId);
                        userPromptConditions.eq(PromptPO::getCreateUserId, CurrentUserHolder.getUserId());
                    }
                })
            );
        } else {
            // 情况2: promptType 已传入，按指定类型查询
            wrapper.eq(PromptPO::getPromptType, promptQuery.getPromptType());

            // 如果查询的是用户提示词 (USER)，则应用 teamId 或 createUserId 过滤
            if (PromptTypeEnum.USER.equals(promptQuery.getPromptType())) {
                wrapper.eq(PromptPO::getTenantId, CurrentUserHolder.getTenantId());
                if (promptQuery.getTeamId() != null) {
                    // 团队空间的提示词
                    wrapper.eq(PromptPO::getTeamId, promptQuery.getTeamId());
                } else {
                    // 个人空间的提示词
                    wrapper.isNull(PromptPO::getTeamId);
                    wrapper.eq(PromptPO::getCreateUserId, CurrentUserHolder.getUserId());
                }
            }
        }

        // 系统提示词按照创建时间升序，用户提示词按照创建时间降序
        if (PromptTypeEnum.SYSTEM.equals(promptQuery.getPromptType())) {
            wrapper.orderByAsc(PromptPO::getCreateTime);
        } else {
            wrapper.orderByDesc(PromptPO::getCreateTime); // 统一按创建时间降序
        }

        return this.getBaseMapper().selectPage(page, wrapper);
    }

    @Override
    public List<PromptVersionPO> findVersionsByPromptId(Long promptId) {
        LambdaQueryWrapper<PromptVersionPO> wrapper = new LambdaQueryWrapper<PromptVersionPO>()
            .eq(PromptVersionPO::getPromptId, promptId)
            .orderByDesc(PromptVersionPO::getCreateTime);
        return promptVersionMapper.selectList(wrapper);
    }

    @Override
    public boolean isTitleUnique(String title, Long teamId, Long excludePromptId) {
        LambdaQueryWrapper<PromptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PromptPO::getTitle, title);

        if (teamId != null) {
            // 团队空间下的标题校验
            queryWrapper.eq(PromptPO::getTeamId, teamId);
        } else {
            // 个人空间下的标题校验 (teamId is null, 结合 createUserId)
            queryWrapper.isNull(PromptPO::getTeamId)
                .eq(PromptPO::getCreateUserId, CurrentUserHolder.getUserId());
        }

        if (excludePromptId != null) {
            // 更新时，排除自身
            queryWrapper.ne(PromptPO::getId, excludePromptId);
        }
        // 只需要检查是否存在，不需要获取完整对象
        return this.getBaseMapper().selectCount(queryWrapper) == 0;
    }

    @Override
    public PromptVersionPO findVersionByPromptId(Long promptId, String version) {
        LambdaQueryWrapper<PromptVersionPO> wrapper = new LambdaQueryWrapper<PromptVersionPO>()
            .eq(PromptVersionPO::getPromptId, promptId)
            .eq(PromptVersionPO::getVersion, version);
        return promptVersionMapper.selectOne(wrapper);
    }

    @Override
    public List<PromptPO> findSystemPrompts() {
        LambdaQueryWrapper<PromptPO> wrapper = new LambdaQueryWrapper<PromptPO>()
            .eq(PromptPO::getPromptType, PromptTypeEnum.SYSTEM)
            .orderByAsc(PromptPO::getCreateTime);
        return this.getBaseMapper().selectList(wrapper);
    }

    @Override
    public void deleteById(Long id) {
        // 删除 Prompt 及其版本
        this.getBaseMapper().deleteById(id);
        promptVersionMapper.delete(new LambdaQueryWrapper<PromptVersionPO>().eq(PromptVersionPO::getPromptId, id));
    }
}
