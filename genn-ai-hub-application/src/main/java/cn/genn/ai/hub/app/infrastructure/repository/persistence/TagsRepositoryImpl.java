package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.TagsAssembler;
import cn.genn.ai.hub.app.application.dto.TagsDTO;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.application.query.TagsQuery;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.TagsMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.TagsRefMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.TagsPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.TagsRefPO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.database.mybatisplus.query.QueryWrapperUtil;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 标签仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class TagsRepositoryImpl extends ServiceImpl<TagsMapper, TagsPO> {

    private final TagsMapper tagsMapper;
    private final TagsRefMapper tagsRefMapper;
    private final TagsAssembler tagsAssembler;
    private final TagsRefRepositoryImpl tagsRefRepository;

    /**
     * 分页查询标签
     *
     * @param query 查询条件
     * @return 标签DTO分页对象
     */
    @IgnoreTenant
    public PageResultDTO<TagsDTO> page(TagsQuery query) {
        QueryWrapper<TagsPO> queryWrapper = buildQueryWrapper(query);
        return tagsAssembler.toPageResult(tagsMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
    }

    /**
     * 创建标签
     *
     * @param tagsPO 标签PO
     * @return 标签ID
     */
    public Long createTag(TagsPO tagsPO) {
        tagsMapper.insert(tagsPO);
        return tagsPO.getId();
    }

    /**
     * 删除标签
     *
     * @param id 标签ID
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTag(Long id) {
        // 先删除标签关联
        QueryWrapper<TagsRefPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TagsRefPO::getTagId, id);
        tagsRefMapper.delete(queryWrapper);

        // 再删除标签本身
        int result = tagsMapper.deleteById(id);
        return result > 0;
    }

    /**
     * 根据ID获取标签
     *
     * @param id 标签ID
     * @return 标签DTO
     */
    public TagsDTO getTag(Long id) {
        TagsPO tagsPO = tagsMapper.selectById(id);
        if (tagsPO == null) {
            throw new BusinessException(MessageCode.TAG_NOT_FOUND, "标签ID: " + id + " 不存在");
        }
        return tagsAssembler.PO2DTO(tagsPO);
    }

    /**
     * 创建标签关联
     *
     */
    @IgnoreTenant
    public void createTagRef(String resourceKey, List<Long> tagIds) {
        if (CollUtil.isEmpty(tagIds)) {
            return;
        }
        //查询标签是否存在
        Long count = tagsMapper.selectCount(Wrappers.<TagsPO>lambdaQuery().in(TagsPO::getId, tagIds));
        if (count != tagIds.size()) {
            throw new CheckException(MessageCode.TAG_NOT_FOUND);
        }
        List<TagsRefPO> refPOS = tagIds.stream().map(tagId -> {
            TagsRefPO refPO = new TagsRefPO();
            refPO.setResourceKey(resourceKey);
            refPO.setTagId(tagId);
            return refPO;
        }).collect(Collectors.toList());
        tagsRefRepository.saveBatch(refPOS);
    }

    /**
     * 删除标签关联
     *
     * @param tagId       标签ID
     * @param resourceKey 资源key
     * @return 是否删除成功
     */
    public Boolean deleteTagRef(Long tagId, String resourceKey) {
        QueryWrapper<TagsRefPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(TagsRefPO::getTagId, tagId)
            .eq(TagsRefPO::getResourceKey, resourceKey);

        int result = tagsRefMapper.delete(queryWrapper);
        return result > 0;
    }

    /**
     * 根据资源key获取关联的标签ID列表
     *
     * @param resourceKey 资源key
     * @return 标签ID列表
     */
    public List<Long> getTagIdsByResourceKey(String resourceKey) {
        QueryWrapper<TagsRefPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(TagsRefPO::getResourceKey, resourceKey)
            .select(TagsRefPO::getTagId);

        return tagsRefMapper.selectObjs(queryWrapper).stream()
            .map(obj -> (Long) obj)
            .toList();
    }

    /**
     * 根据资源key获取关联的标签ID列表
     */
    public Map<String, List<Long>> getTagIdsByResourceKey(List<String> resourceKeys) {
        QueryWrapper<TagsRefPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .in(TagsRefPO::getResourceKey, resourceKeys);
        List<TagsRefPO> tagsRefPOS = tagsRefMapper.selectList(Wrappers.<TagsRefPO>lambdaQuery().in(TagsRefPO::getResourceKey, resourceKeys));
        if (CollUtil.isEmpty(tagsRefPOS)) {
            return Collections.emptyMap();
        }
        return tagsRefPOS.stream().collect(Collectors.groupingBy(TagsRefPO::getResourceKey, Collectors.mapping(TagsRefPO::getTagId, Collectors.toList())));
    }

    /**
     * 根据标签ID列表和标签类型获取关联的资源key列表
     *
     * @param tagIds 标签ID列表
     * @return 资源key列表
     */
    public Set<String> getResourceKeysByTagIds(List<Long> tagIds) {
        if (CollUtil.isEmpty(tagIds)) {
            return Collections.emptySet();
        }

        QueryWrapper<TagsRefPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(TagsRefPO::getTagId, tagIds);
        List<TagsRefPO> tagsRefs = tagsRefMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(tagsRefs)) {
            return Collections.emptySet();
        }

        return tagsRefs.stream()
            .map(TagsRefPO::getResourceKey)
            .collect(Collectors.toSet());
    }

    /**
     * 根据标签名称获取关联的资源key列表
     *
     * @return 资源key列表
     */
    public Set<String> getResourceKeysByNameConditions(TagsQuery query) {
        if (CharSequenceUtil.isEmpty(query.getName())) {
            return Collections.emptySet();
        }

        // 先根据标签名称查询标签ID
        QueryWrapper<TagsPO> tagWrapper = buildQueryWrapper(query);
        List<TagsPO> tags = tagsMapper.selectList(tagWrapper);

        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptySet();
        }

        // 根据标签ID查询关联的资源key
        List<Long> tagIds = tags.stream().map(TagsPO::getId).collect(Collectors.toList());
        return getResourceKeysByTagIds(tagIds);
    }

    /**
     * 更新标签
     *
     * @param tagsPO 标签PO
     * @return 是否更新成功
     */
    public Boolean updateTag(TagsPO tagsPO) {
        // 检查标签是否存在
        TagsPO existingTag = tagsMapper.selectById(tagsPO.getId());
        if (existingTag == null) {
            throw new CheckException(MessageCode.TAG_NOT_FOUND);
        }

        TagsPO updatePO = new TagsPO();
        updatePO.setId(tagsPO.getId());
        updatePO.setName(tagsPO.getName());
        updatePO.setAvatar(tagsPO.getAvatar());

        int result = tagsMapper.updateById(updatePO);
        return result > 0;
    }


    public void deleteTagRefByResource(String resourceKey, List<Long> tagIds) {
        QueryWrapper<TagsRefPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(TagsRefPO::getResourceKey, resourceKey)
            .in(TagsRefPO::getTagId, tagIds);
        tagsRefMapper.delete(queryWrapper);
    }


    /**
     * 根据查询条件构建查询对象
     *
     * @param query 查询条件
     * @return 查询对象
     */
    private QueryWrapper<TagsPO> buildQueryWrapper(TagsQuery query) {
        QueryWrapper<TagsPO> queryWrapper = QueryWrapperUtil.build(query);
        if (query.getTagType() == TagTypeEnum.GENERAL_TOOL) {
            return queryWrapper;
        }
        if (query.getTeamId() != null) {
            queryWrapper.eq("team_id", query.getTeamId());
        }else {
            queryWrapper.eq("create_user_id", CurrentUserHolder.getUserId());
            queryWrapper.isNull("team_id");
        }
        if (query.getTagType() == TagTypeEnum.GENERAL_TOOL) {
            queryWrapper.eq("tenant_id", 0L);
        }else {
            queryWrapper.eq("tenant_id", CurrentUserHolder.getTenantId());
        }
        return queryWrapper;
    }

    @IgnoreTenant
    public List<TagsPO> listByIdsNoTenantIds(List<Long> ids) {
        return listByIds(ids);
    }
}
