package cn.genn.ai.hub.app.infrastructure.config;

import cn.genn.ai.cerebro.service.CerebroService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.JdkClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;

import java.net.http.HttpClient;

@Configuration
public class CerebroServiceConfiguration {

    @Bean
    public WebClient webClient() {
        HttpClient httpClient = HttpClient.newBuilder().version(HttpClient.Version.HTTP_1_1).build();
        return WebClient.builder()
            .codecs(configurer -> {
                // 设置缓冲区大小，支持大文件传输
                configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024); // 10MB
            })
            .clientConnector(new JdkClientHttpConnector(httpClient))
            .build();
    }

    @Bean
    public CerebroService cerebroService(WebClient webClient, GennAIHubProperties gennAIHubProperties) {
        return new CerebroService(webClient, gennAIHubProperties.getAgent().getInvokeDomain());
    }


}
