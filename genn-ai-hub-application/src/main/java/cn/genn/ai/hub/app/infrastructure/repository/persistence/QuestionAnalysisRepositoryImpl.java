package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.QuestionAnalysisAssembler;
import cn.genn.ai.hub.app.application.command.QuestionAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.question.DepartmentQuestionCountDTO;
import cn.genn.ai.hub.app.application.dto.question.DepartmentUserDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionAnalysisDTO;
import cn.genn.ai.hub.app.application.query.DepartmentQuestionCountQuery;
import cn.genn.ai.hub.app.application.query.QuestionAnalysisQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionAnalysisMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionAnalysisPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionInfoPO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Repository
@IgnoreTenant
@RequiredArgsConstructor
public class QuestionAnalysisRepositoryImpl extends ServiceImpl<QuestionAnalysisMapper, QuestionAnalysisPO> {


    private final QuestionAnalysisMapper analysisMapper;
    private final QuestionAnalysisAssembler questionAnalysisAssembler;
    private final QuestionInfoMapper questionInfoMapper;

    public List<QuestionAnalysisDTO> analysisDTOs(QuestionAnalysisQuery query) {
        if (query.getSources() != null && query.getSources().isEmpty()) {
            return Lists.newArrayList();
        }
        List<QuestionAnalysisDTO> analysis = analysisMapper.getAnalysis(query);
        if (CollUtil.isEmpty(analysis)) {
            return Lists.newArrayList();
        }
        List<String> types = analysis
            .stream()
            .map(QuestionAnalysisDTO::getType)
            .toList();

        LambdaQueryWrapper<QuestionAnalysisPO> wrapper = new QueryWrapper<QuestionAnalysisPO>()
            .lambda()
            .eq(QuestionAnalysisPO::getAppId, query.getAppId())
            .in(CollUtil.isNotEmpty(types), QuestionAnalysisPO::getType, types)
            .in(CollUtil.isNotEmpty(query.getSources()), QuestionAnalysisPO::getSource, query.getSources());
        List<QuestionAnalysisDTO> sourceDTO = questionAnalysisAssembler.PO2DTO(analysisMapper.selectList(wrapper));

        Map<String, List<QuestionAnalysisDTO>> typeMap = sourceDTO.stream().collect(Collectors.groupingBy(QuestionAnalysisDTO::getType));
        for (QuestionAnalysisDTO anl : analysis) {
            List<Long> relIds = Lists.newArrayList();
            List<QuestionAnalysisDTO> anlDTO = typeMap.get(anl.getType());
            for (QuestionAnalysisDTO source : anlDTO) {
                relIds.addAll(source.getRelInfoId());
            }
            anl.setRelInfoId(relIds);
        }
        return analysis;
    }

    public void addBatch(List<QuestionAnalysisCommand> commands) {
        if (CollUtil.isEmpty(commands)) {
            return;
        }
        List<QuestionAnalysisPO> adds = Lists.newArrayList();
        List<QuestionAnalysisPO> update = Lists.newArrayList();

        for (QuestionAnalysisCommand command : commands) {
            List<Long> newRelIds = command.getRelInfoIds();
            QuestionAnalysisPO po = questionAnalysisAssembler.convertPO(command);
            po.setCount(newRelIds.size());
            QuestionAnalysisDTO oldDTO = querySourcePO(command.getAppId(), command.getSource(), command.getType());
            if (oldDTO != null) {
                po.setId(oldDTO.getId());
                HashSet<Long> oldRelIds = new HashSet<>(oldDTO.getRelInfoId());
                oldRelIds.addAll(newRelIds);
                po.setRelInfoId(JsonUtils.toJson(Lists.newArrayList(oldRelIds)));
                po.setCount(oldRelIds.size());
                update.add(po);
            } else {
                po.setRelInfoId(JsonUtils.toJson(newRelIds));
                adds.add(po);
            }
        }
        if (CollUtil.isNotEmpty(adds)) {
            saveBatch(adds);
        }
        if (CollUtil.isNotEmpty(update)) {
            updateBatchById(update);
        }
        //回写问题类型到问题表
        for (QuestionAnalysisCommand command : commands) {
            if(CollUtil.isNotEmpty(command.getRelInfoIds()) && StrUtil.isNotBlank(command.getType())){
                questionInfoMapper.updateTypeByIds(command.getRelInfoIds(),command.getType());
            }
        }
    }

    public QuestionAnalysisDTO querySourcePO(String appId, String source, String type) {
        LambdaQueryWrapper<QuestionAnalysisPO> wrapper = new QueryWrapper<QuestionAnalysisPO>()
            .lambda()
            .eq(QuestionAnalysisPO::getAppId, appId)
            .eq(QuestionAnalysisPO::getSource, source)
            .eq(QuestionAnalysisPO::getType, type);
        return questionAnalysisAssembler.PO2DTO(analysisMapper.selectOne(wrapper));
    }

    public List<String> queryTypes(String appId) {
        return Optional.ofNullable(analysisMapper.getAllTypes(appId)).orElse(Lists.newArrayList());
    }


    public List<QuestionAnalysisDTO> queryByAppId(String appId, String source) {
        LambdaQueryWrapper<QuestionAnalysisPO> wrapper = new QueryWrapper<QuestionAnalysisPO>()
            .lambda()
            .eq(QuestionAnalysisPO::getAppId, appId)
            .eq(QuestionAnalysisPO::getSource, source);
        return questionAnalysisAssembler.PO2DTO(analysisMapper.selectList(wrapper));
    }


}
