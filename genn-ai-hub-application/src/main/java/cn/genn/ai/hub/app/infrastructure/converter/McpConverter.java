package cn.genn.ai.hub.app.infrastructure.converter;

import cn.genn.ai.hub.app.domain.mcp.model.entity.Mcp;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * MCP转换器，负责领域实体与持久化对象之间的转换
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface McpConverter {

    McpConverter INSTANCE = Mappers.getMapper(McpConverter.class);

    /**
     * 将领域实体转换为持久化对象
     *
     * @param mcp 领域实体
     * @return 持久化对象
     */
    McpPO toMcpPO(Mcp mcp);

    /**
     * 将持久化对象转换为领域实体
     *
     * @param mcpPO 持久化对象
     * @return 领域实体
     */
    Mcp toMcp(McpPO mcpPO);
}
