package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.domain.mcp.model.valobj.McpConfig;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.typehandler.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MCP分组PO对象
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "mcp_group", autoResultMap = true)
public class McpGroupPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 分组名称
     */
    @TableField("name")
    private String name;

    /**
     * 分组描述
     */
    @TableField("description")
    private String description;

    /**
     * 分组唯一标识
     */
    @TableField("group_key")
    private String groupKey;

    /**
     * 服务配置
     */
    @TableField(value = "server_config", typeHandler = JacksonTypeHandler.class)
    private McpConfig serverConfig;

    /**
     * 发布状态
     */
    @TableField("publish")
    private BooleanTypeEnum publish;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;
}
