package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallContentDTO;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallConfirmEnum;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallTypeEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@TableName(value = "rtc_pitfall_item", autoResultMap = true)
public class RTCPitfallItemPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 隐患信息id
     */
    @TableField("pitfall_id")
    private Long pitfallId;

    /**
     * 隐患编号
     */
    @TableField("pitfall_no")
    private String pitfallNo;

    /**
     * 类型,1:GENERAL-一般隐患,2:MAJOR-重大隐患
     */
    @TableField("pitfall_type")
    private RTCPitfallTypeEnum pitfallType;

    /**
     * 确认状态,0:UN_CONFIRM-待确认,1:NO_NEED_RESOLVE-无需解决,2:NEED_RESOLVE-需要解决
     */
    @TableField("confirm_status")
    private RTCPitfallConfirmEnum confirmStatus;

    /**
     * 隐患内容
     */
    @TableField(value = "pitfall_content", typeHandler = JacksonTypeHandler.class)
    private RTCPitfallContentDTO pitfallContent;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;
}
