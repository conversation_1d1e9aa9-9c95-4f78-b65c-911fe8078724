package cn.genn.ai.hub.app.infrastructure.external.wechat;

import cn.genn.ai.hub.app.infrastructure.constant.WxConstants;
import cn.genn.ai.hub.app.application.dto.wx.GetAccessTokenDTO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 飞书客户端管理
 * @date 2025-04-14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WxAccessTokenManager {

    private final StringRedisTemplate stringRedisTemplate;

    private final RestTemplate restTemplate;

    public String getAccessToken(String appId, String appSecret) {
        return getAccessToken(appId, appSecret, false);
    }

    /**
     * 获取access_token
     * @param appId appId
     * @param appSecret appSecret
     * @param isForceRefresh 是否强制刷新
     * @return access_token
     */
    public String getAccessToken(String appId, String appSecret, Boolean isForceRefresh) {
        String url = String.format("%s?grant_type=client_credential&appid=%s&secret=%s", WxConstants.WX_GET_ACCESS_TOKEN_URL, appId, appSecret);
        String redisKey = WxConstants.WX_MP_ACCESS_TOKEN_KEY + appId;
        try {
            String accessToken = stringRedisTemplate.opsForValue().get(redisKey);
            if (StrUtil.isEmpty(accessToken) || isForceRefresh) {
                // 调用微信接口获取access_token
                String response = restTemplate.getForObject(url, String.class);
                log.info("获取access_token, 请求: {}, 接口返回: {}", url, response);
                // 解析response，获取access_token
                GetAccessTokenDTO accessTokenOutputStream = JsonUtils.parse(response, GetAccessTokenDTO.class);
                if (StrUtil.isNotBlank(accessTokenOutputStream.getAccessToken())) {
                    // 存入redis
                    stringRedisTemplate.opsForValue().set(redisKey, accessTokenOutputStream.getAccessToken(), WxConstants.WX_MP_ACCESS_TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                }
                // 返回access_token
                return accessTokenOutputStream.getAccessToken();
            } else {
                return accessToken;
            }
        } catch (Exception ex) {
            log.error("获取access_token失败, 请求: {}, 异常: {}", url, ex.getMessage());
            return null;
        }
    }
}
