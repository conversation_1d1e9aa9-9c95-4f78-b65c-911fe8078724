package cn.genn.ai.hub.app.infrastructure.external.feishu.event;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 事件回调枚举
 * @date 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum EventCallbackEnum {

    BOT_ADD("im.chat.member.bot.added_v1", "机器人进群事件"),
    RECEIVE_MESSAGE("im.message.receive_v1", "接收消息事件"),
    P2P_CHAT_CREATE("p2p_chat_create", "用户和机器人的会话首次被创建事件"),
    CARD_ACTION_TRIGGER("card.action.trigger", "卡片操作事件"),

    ;

    private final String eventType;

    private final String desc;

    private static final Map<String, EventCallbackEnum> VALUES = new HashMap<>();
    static {
        for (final EventCallbackEnum item : EventCallbackEnum.values()) {
            VALUES.put(item.getEventType(), item);
        }
    }

    public static EventCallbackEnum of(final String value) {
        return VALUES.get(value);
    }
}
