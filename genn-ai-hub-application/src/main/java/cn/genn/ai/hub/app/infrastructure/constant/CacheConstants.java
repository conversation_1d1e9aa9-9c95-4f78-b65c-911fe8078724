package cn.genn.ai.hub.app.infrastructure.constant;

/**
 * 缓存相关key定义
 *
 * <AUTHOR>
 */
public class CacheConstants {

    /**
     * 统一的缓存前缀
     */
    public static final String CACHE_PRE = "GENN:AI:HUB";
    public static final String LOCK_PRE = "GENN:LOCK";


    /**
     * 具体的缓存key
     */
    public static final String TOS_PRE_SIGNED_URL_KEY = CACHE_PRE + ":TOS:PRE:SIGNED:URL:KEY:";

    public static final String FS_LOGIN_STATE = CACHE_PRE + ":FS:LOGIN:STATE:";

    public static final String FS_VOICE_SPEECH = CACHE_PRE + ":FS:VOICE:SPEECH:";
    public static final String FS_VOICE_SPEECH_LOCK = LOCK_PRE + ":FS:VOICE:SPEECH:";

    /**
     * 分布式锁相关key
     */
    public static final String TASK_PROCESS_LOCK_KEY = LOCK_PRE + ":TASK:PROCESS:KEY";
    // 获取锁超时时长
    public static final Long LOCK_ORDER_STATUS_CHANGE_TIMEOUT = 5 * 1000L;

    /**
     * 智能体锁定key
     */
    public static final String AGENT_LOCK_KEY = CACHE_PRE + ":LOCK:AGENT:";
    public static final Long AGENT_LOCK_STATUS_CHANGE_TIMEOUT = 30 * 1000L;


    /**
     * ------------------------- 资源缓存------------------------------
     */
    public static final String AGENT_TENANT_ID_CACHE = CACHE_PRE + ":AGENT:TENANT";
    public static final String SYSTEM_CONFIG_TYPE_CACHE = CACHE_PRE + ":SYSTEM_CONFIG:TYPE";
    public static final String SYSTEM_CONFIG_KEY_CACHE = CACHE_PRE + ":SYSTEM_CONFIG:KEY";

    public static final String KB_TENANT_ID_CACHE = CACHE_PRE + ":KB:TENANT";

    /**
     * ------------------------- 飞书多维表格数据缓存------------------------------
     */
    public static final String FS_MULTI_TABLE_DATA_CACHE = CACHE_PRE + ":FS:MULTI:TABLE:";

}
