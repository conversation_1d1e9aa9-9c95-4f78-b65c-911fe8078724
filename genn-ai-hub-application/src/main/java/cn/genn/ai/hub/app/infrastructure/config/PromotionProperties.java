package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Data;

@Data
public class PromotionProperties {

    private String appId = "cli_a776dad51039501c";

    private String appSecret ="ciHMqMaRNFIc11fapPGOYeELVDVHCPT0";

    private String tableUrl = "https://qvmyfyf7txd.feishu.cn/wiki/L9oywpeV8ilYMpkew5ncKdOZnfh?table=tblwYEgXncTXjCZt";

    private String createUser;
    private String todayWorkDifficulty;
    private String studyAndInnovation;
    private String centerDepartment;
    private String department;
    private String todayWorkCompletion;
    private String jobResponsibility;
    private String reportDate;

}
