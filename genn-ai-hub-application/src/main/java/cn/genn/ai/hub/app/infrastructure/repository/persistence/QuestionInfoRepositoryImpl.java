package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.QuestionInfoAssembler;
import cn.genn.ai.hub.app.application.dto.question.DepartmentQuestionCountDTO;
import cn.genn.ai.hub.app.application.dto.question.DepartmentUserDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionDetailDTO;
import cn.genn.ai.hub.app.application.dto.question.UserQuestionCountDTO;
import cn.genn.ai.hub.app.application.query.DepartmentQuestionCountQuery;
import cn.genn.ai.hub.app.application.query.UserQuestionCountQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionInfoPO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * QuestionInfo仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class QuestionInfoRepositoryImpl extends ServiceImpl<QuestionInfoMapper, QuestionInfoPO> {

    private final QuestionInfoMapper questionInfoMapper;
    private final QuestionInfoAssembler questionInfoAssembler;

    /**
     * 获取用户统计和问题详情
     */
    public List<UserQuestionCountDTO> getTopUsersWithDetails(UserQuestionCountQuery query) {
        // 1. 获取用户统计信息（top10）
        List<UserQuestionCountDTO> userStats = questionInfoMapper.getTopUsers(query);

        if (CollUtil.isEmpty(userStats)) {
            return Lists.newArrayList();
        }

        // 3. 提取所有用户ID
        List<Long> userIds = userStats.stream()
            .map(UserQuestionCountDTO::getUserId)
            .toList();

        // 4. 批量查询所有用户的问题详情
        LambdaQueryWrapper<QuestionInfoPO> wrapper = new QueryWrapper<QuestionInfoPO>()
            .lambda()
            .eq(QuestionInfoPO::getAppId, query.getAppId())
            .in(CollUtil.isNotEmpty(query.getSources()), QuestionInfoPO::getSource, query.getSources())
            .between(ObjUtil.isNotNull(query.getStartTime()) && ObjUtil.isNotNull(query.getEndTime()), QuestionInfoPO::getCreateTime, query.getStartTime(), query.getEndTime())
            .in(QuestionInfoPO::getUserId, userIds);

        List<QuestionInfoPO> allQuestions = questionInfoMapper.selectList(wrapper);

        // 5. 按用户ID分组问题详情
        Map<Long, List<QuestionInfoPO>> userQuestionsMap = allQuestions.stream()
            .collect(Collectors.groupingBy(QuestionInfoPO::getUserId));

        // 6. 组装数据
        for (UserQuestionCountDTO userStat : userStats) {
            List<QuestionInfoPO> userQuestions = userQuestionsMap.get(userStat.getUserId());
            List<QuestionDetailDTO> questionDetails = questionInfoAssembler.PO2DetailDTO(userQuestions);
            userStat.setQuestionDetails(questionDetails);
            //获取出现次数最多的来源
            String mostType = questionDetails.stream()
                    .map(QuestionDetailDTO::getType)
                    .filter(type -> type != null && !type.trim().isEmpty())
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                    .entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(null);
            userStat.setType(mostType);

        }
        return userStats;
    }


    /**
     * 获取部门top10统计和人员详情
     */
    public List<DepartmentQuestionCountDTO> getDepartmentWithDetails(DepartmentQuestionCountQuery query) {
        // 1. 获取部门统计信息（包含主要来源）
        List<DepartmentQuestionCountDTO> departmentStats = questionInfoMapper.getDepartmentTop10(query);

        if (CollUtil.isEmpty(departmentStats)) {
            return Lists.newArrayList();
        }

        // 3. 提取所有部门名称
        List<String> departments = departmentStats.stream()
            .map(DepartmentQuestionCountDTO::getFirstDepartment)
            .toList();

        // 4. 批量查询所有部门的人员统计
        List<DepartmentUserDTO> allUsers = questionInfoMapper.getDepartmentUsers(query, departments);

        // 5. 按部门分组人员信息
        Map<String, List<DepartmentUserDTO>> departmentUsersMap = allUsers.stream()
            .collect(Collectors.groupingBy(DepartmentUserDTO::getFirstDepartment));

        // 6. 组装数据
        for (DepartmentQuestionCountDTO deptStat : departmentStats) {
            List<DepartmentUserDTO> deptUsers = departmentUsersMap.get(deptStat.getFirstDepartment());
            deptStat.setUsers(deptUsers != null ? deptUsers : Lists.newArrayList());

        }

        return departmentStats;
    }
}
