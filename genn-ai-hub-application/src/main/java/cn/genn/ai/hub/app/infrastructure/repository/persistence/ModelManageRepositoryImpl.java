package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.ModelManageAssembler;
import cn.genn.ai.hub.app.application.dto.ModelManageDTO;
import cn.genn.ai.hub.app.application.dto.request.ModelManageQuery;
import cn.genn.ai.hub.app.application.enums.ModelTypeEnum;
import cn.genn.ai.hub.app.infrastructure.repository.config.ModelConfigUtils;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ModelManageMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelManagePO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageResultDTO;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 运单仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class ModelManageRepositoryImpl extends ServiceImpl<ModelManageMapper, ModelManagePO> {

    @Resource
    private ModelManageMapper manageMapper;
    @Resource
    private ModelManageAssembler mangleAssembler;


    public ModelManageDTO getInfo(Long id) {
        return mangleAssembler.convert(manageMapper.selectById(id));
    }

    public List<ModelManageDTO> getActiveModel() {
        QueryWrapper<ModelManagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(ModelManagePO::getIsActive, true)
            .eq(ModelManagePO::getDeleted, DeletedEnum.NOT_DELETED)
            .orderByDesc(ModelManagePO::getIsDefault)
        ;
        return mangleAssembler.convert(manageMapper.selectList(queryWrapper));
    }

    public List<ModelManagePO> getActives() {
        QueryWrapper<ModelManagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(ModelManagePO::getIsActive, true)
            .eq(ModelManagePO::getDeleted, DeletedEnum.NOT_DELETED)
            .orderByDesc(ModelManagePO::getIsDefault)
        ;
        return manageMapper.selectList(queryWrapper);
    }


    public Long create(ModelManageDTO create) {
        ModelManagePO modelPO = mangleAssembler.convertPo(create);
        modelPO.setCommonConfig(ModelConfigUtils.parseConfig(create.getType(), create.getCommonConfig()));
        manageMapper.insert(modelPO);
        return modelPO.getId();
    }

    public List<ModelManageDTO> findActiveModel(ModelTypeEnum type) {
        QueryWrapper<ModelManagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(ModelManagePO::getType, type)
            .eq(ModelManagePO::getIsActive, true)
            .eq(ModelManagePO::getDeleted, DeletedEnum.NOT_DELETED)
        ;
        return mangleAssembler.convert(manageMapper.selectList(queryWrapper));
    }

    public ModelManageDTO findByModelAndProviderId(String model, Long providerId) {
        QueryWrapper<ModelManagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(ModelManagePO::getModel, model)
            .eq(ModelManagePO::getProviderId, providerId)
            .eq(ModelManagePO::getDeleted, DeletedEnum.NOT_DELETED)
        ;
        return mangleAssembler.convert(manageMapper.selectOne(queryWrapper));
    }

    public boolean updateConfig(ModelManageDTO update) {
        ModelManagePO modelPO = mangleAssembler.convertPo(update);
        if (StringUtils.isBlank(modelPO.getApiKey())) {
            modelPO.setApiKey(null);
        }
        modelPO.setCommonConfig(ModelConfigUtils.parseConfig(update.getType(), update.getCommonConfig()));
        return updateById(modelPO);
    }

    public Boolean active(Long id) {
        UpdateWrapper<ModelManagePO> update = new UpdateWrapper<>();
        update.lambda()
            .eq(ModelManagePO::getId, id)
            .set(ModelManagePO::getIsActive, true)
        ;
        return update(update);
    }

    public Boolean stop(Long id) {
        UpdateWrapper<ModelManagePO> update = new UpdateWrapper<>();
        update.lambda()
            .eq(ModelManagePO::getId, id)
            .set(ModelManagePO::getIsActive, false)
            .set(ModelManagePO::getIsDefault, false)
        ;
        return update(update);
    }

    public PageResultDTO<ModelManageDTO> getPage(ModelManageQuery query) {
        return mangleAssembler.toPageResult(manageMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), buildModelQuery(query)));
    }


    private QueryWrapper<ModelManagePO> buildModelQuery(ModelManageQuery query) {
        QueryWrapper<ModelManagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(StringUtils.isNotBlank(query.getModel()), ModelManagePO::getModel, query.getModel())
            .like(StringUtils.isNotBlank(query.getName()), ModelManagePO::getName, query.getName())
            .eq(query.getType() != null, ModelManagePO::getType, query.getType())
            .eq(query.getProviderId() != null, ModelManagePO::getProviderId, query.getProviderId())
            .eq(query.getIsActive() != null, ModelManagePO::getIsActive, query.getIsActive())
            .eq(ModelManagePO::getDeleted, DeletedEnum.NOT_DELETED.getCode())
            .orderByDesc(ModelManagePO::getId);
        ;
        return queryWrapper;
    }

    public void updateModelDefault(List<Long> pkIds, Boolean isDefault) {
        if (CollectionUtils.isEmpty(pkIds)) {
            return;
        }
        UpdateWrapper<ModelManagePO> update = new UpdateWrapper<>();
        update.lambda()
            .in(ModelManagePO::getId, pkIds)
            .set(ModelManagePO::getIsDefault, isDefault)
        ;
        update(update);
    }

    public Boolean delete(Long id) {
        UpdateWrapper<ModelManagePO> update = new UpdateWrapper<>();
        update.lambda()
            .in(ModelManagePO::getId, id)
            .set(ModelManagePO::getDeleted, true)
        ;
        return update(update);
    }

    public void setDefaultModel(Long id) {
        ModelManagePO model = getById(id);
        if (model.getIsDefault()) {
            return;
        }
        List<ModelManageDTO> models = findActiveModel(model.getType());
        if (CollectionUtils.isNotEmpty(models)) {
            ModelManageDTO dto = models.getFirst();
            updateModelDefault(Lists.newArrayList(dto.getId()), true);
        }
    }
}
