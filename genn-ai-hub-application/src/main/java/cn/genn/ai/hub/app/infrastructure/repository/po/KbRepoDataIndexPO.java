package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.IndexTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * KbRepoDataIndexPO对象
 *
 * <AUTHOR>
 * @desc 知识库数据条目索引
 */
@Data
@TableName(value = "kb_repo_data_index", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class KbRepoDataIndexPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 知识库 ID，标识数据所属的知识库
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 知识集合 ID，与 collection 表关联，表示数据所属的集合
     */
    @TableField("collection_id")
    private Long collectionId;

    /**
     * 数据条目唯一标识
     */
    @TableField("data_key")
    private String dataKey;

    /**
     * 索引唯一标识
     */
    @TableField("index_key")
    private String indexKey;

    /**
     * 索引类型, default:DEFAULT-默认, customer:CUSTOMER-自定义
     */
    @TableField("index_type")
    private IndexTypeEnum indexType;

    /**
     * 索引优先级，越大优先级越高
     */
    @TableField("index_sort")
    private Integer indexSort;

    /**
     * 自定义索引内容
     */
    @TableField("index_data")
    private String indexData;

    /**
     * 索引算法处理状态,0:WAIT-未处理, 2:DONE-已处理
     */
    @TableField("handle_status")
    private HandleStatusEnum handleStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

