package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoDataIndexAssembler;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.IndexTypeEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoDataIndexMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Date: 2025/3/7
 * @Author: kangjian
 */
@Repository
public class KbRepDataIndexRepositoryImpl extends ServiceImpl<KbRepoDataIndexMapper, KbRepoDataIndexPO> {

    @Resource
    private KbRepoDataIndexMapper mapper;
    @Resource
    private KbRepoDataIndexAssembler assembler;

    public List<KbRepoDataIndexPO> selectDefaultIndexFromCollection(Long repoId, Long collectionId) {
        QueryWrapper<KbRepoDataIndexPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collectionId)
            .eq(KbRepoDataIndexPO::getIndexType, IndexTypeEnum.DEFAULT)
            .eq(KbRepoDataIndexPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return mapper.selectList(queryWrapper);
    }

    public List<KbRepoDataIndexPO> selectCustomerIndexFromCollection(Long repoId, Long collectionId) {
        QueryWrapper<KbRepoDataIndexPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collectionId)
            .eq(KbRepoDataIndexPO::getIndexType, IndexTypeEnum.CUSTOMER)
            .eq(KbRepoDataIndexPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return mapper.selectList(queryWrapper);
    }

    public List<KbRepoDataIndexPO> selectCustomerIndexFromCollection(Long repoId, Long collectionId, String dataKey) {
        QueryWrapper<KbRepoDataIndexPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collectionId)
            .eq(KbRepoDataIndexPO::getIndexType, IndexTypeEnum.CUSTOMER)
            .eq(KbRepoDataIndexPO::getDataKey, dataKey)
            .eq(KbRepoDataIndexPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return mapper.selectList(queryWrapper);
    }

    @IgnoreTenant
    public List<KbRepoDataIndexPO> selectDataIndexOfDataKey(Long repoId, Long collectionId, String dataKey) {
        QueryWrapper<KbRepoDataIndexPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collectionId)
            .eq(KbRepoDataIndexPO::getDataKey, dataKey)
            .eq(KbRepoDataIndexPO::getDeleted, DeletedTypeEnum.NOT_DELETED)
            .orderByAsc(KbRepoDataIndexPO::getCreateTime);
        return mapper.selectList(queryWrapper);
    }

    public boolean updateHandleStatusOfDataKey(Long repoId, Long collectionId, List<String> dataKey, HandleStatusEnum handleStatus) {
        UpdateWrapper<KbRepoDataIndexPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collectionId)
            .in(KbRepoDataIndexPO::getDataKey, dataKey)
            .set(KbRepoDataIndexPO::getHandleStatus, handleStatus);
        return mapper.update(null, updateWrapper) > 0;
    }

    @IgnoreTenant
    public KbRepoDataIndexPO queryByIndexKey(String indexKey) {
        QueryWrapper<KbRepoDataIndexPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataIndexPO::getIndexKey, indexKey)
            .eq(KbRepoDataIndexPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return mapper.selectOne(queryWrapper);
    }

    public void deleteDataIndexVectorOfData(Long repoId, Long collectionId, String dataKey) {
        QueryWrapper<KbRepoDataIndexPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collectionId)
            .eq(KbRepoDataIndexPO::getDataKey, dataKey);
        mapper.delete(queryWrapper);
    }

    public void removeByCollId(Long repoId, Long collId) {
        QueryWrapper<KbRepoDataIndexPO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collId)
        ;
         mapper.delete(queryWrapper);
    }
}
