package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.ModelTypeEnum;
import cn.genn.ai.hub.app.infrastructure.repository.config.ModelConfig;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * ModelManagePO对象
 *
 * <AUTHOR>
 * @desc 模型管理
 */
@Data
@TableName(value = "model_manage", autoResultMap = true)
public class ModelManagePO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 模型key
     */
    @TableField("model")
    private String model;

    /**
     * 模型名称
     */
    @TableField("name")
    private String name;

    /**
     * 别名
     */
    @TableField("alias")
    private String alias;

    /**
     * 模型类型：llm、embedding、StepFun、stt、rerank
     *
     * @Schema(description = "模型类型：1:语言模型、2:索引模型、3:语音合成、4:语音识别、5:重排模型")
     */
    @TableField("type")
    private ModelTypeEnum type;
    /**
     * 供应商ID
     */
    @TableField("provider_id")
    private Long providerId;

    /**
     * 供应商名称
     */
    @TableField("provider_name")
    private String providerName;


    /**
     * 请求地址
     */
    @TableField("base_url")
    private String baseUrl;

    /**
     * 请求 key
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * 请求路径
     */
    @TableField("api_path")
    private String apiPath;

    /**
     * 是否激活
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 是否默认
     */
    @TableField("is_default")
    private Boolean isDefault;

    /**
     * 模型通用配置
     */
    @TableField(value = "common_config", typeHandler = JacksonTypeHandler.class)
    private ModelConfig commonConfig;

    /**
     * 自定义请求参数配置
     */
    @TableField("param_config")
    private String paramConfig;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建人名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}

