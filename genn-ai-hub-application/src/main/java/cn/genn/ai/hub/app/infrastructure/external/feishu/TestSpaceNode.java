package cn.genn.ai.hub.app.infrastructure.external.feishu;

import cn.genn.ai.hub.app.application.dto.feishu.*;
import cn.genn.ai.hub.app.application.enums.feishu.BlockType;
import cn.genn.ai.hub.app.application.query.FSSheetContentQuery;
import cn.genn.ai.hub.app.application.query.FSSpaceQuery;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.plugin.common.exception.AIPluginException;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.google.gson.reflect.TypeToken;
import com.lark.oapi.Client;
import com.lark.oapi.service.docx.v1.model.*;
import com.lark.oapi.service.drive.v1.model.BatchGetTmpDownloadUrlMediaReq;
import com.lark.oapi.service.drive.v1.model.BatchGetTmpDownloadUrlMediaResp;
import com.lark.oapi.service.drive.v1.model.DownloadFileReq;
import com.lark.oapi.service.drive.v1.model.DownloadFileResp;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetReq;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetResp;
import com.lark.oapi.service.wiki.v2.model.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class TestSpaceNode {

//    private static final String APP_ID = "cli_a7634b590eb4500d";
//    private static final String APP_SECRET = "mm6pFlflMS4OlxfAf6vtff1dz5mDu5HI";
    private static final String APP_ID = "cli_a7634b590eb4500d";
    private static final String APP_SECRET = "mm6pFlflMS4OlxfAf6vtff1dz5mDu5HI";
    /**
     * 创建 LarkClient 对象，用于请求OpenAPI。
     * Create LarkClient object for requesting OpenAPI
     */
    private static final Client client = new Client.Builder(APP_ID, APP_SECRET).build();
    private static final Logger log = LoggerFactory.getLogger(FSDocParser.class);

    private static final ThreadLocal<String> tl = new ThreadLocal<>();

    private static final Map<String, Block> BLOCK_MAP = new HashMap<>(256);
    private static final Map<String, Client> CLIENT_MAP = new HashMap<>(4);
    private static final Map<String, Integer> AUTO_ORDER = new HashMap<>(4);

    public static void main(String[] args) throws Exception {
        file();
//        String objToken = extractAppTokenFromUrl(url, client);
//        String sheetId = extractSheetId(objToken);
//        FSUtil.getFSNodeToken(url, client);
//        System.out.println();
//        FSSheetContentQuery query = new FSSheetContentQuery();
//        query.setObjToken("BOOus1oPChIxnWtEqYccKBMynFh");
//        query.setSheetId("6caa66");
//        query.setRanges("A1:D4");
//        getSheetContent(query);
//        FSSpaceQuery sq = new FSSpaceQuery();
//        sq.setFolderToken("D6trwT6cwie9vCkaT0yc6dJgnYf");
//        ListSpaceNodeRespBody spaceList = getSpaceList(sq);
//        System.out.println();
//        testMarkStr();
    }
    public static final String  url = "Qbogwq5wEi5uMmklqvvcJNDwnZd";
    private static String extractSheetId(String objToken) {
        // 提取 nodeToken
        Pattern pattern = Pattern.compile("sheet=([\\w-]+)(?:\\?|$)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return getDefaultSheetId(objToken, client);
    }

    public static ByteArrayOutputStream file() throws Exception {
        // 创建请求对象
        DownloadFileReq req = DownloadFileReq.newBuilder()
            .fileToken("HHrobdItAowwVBxwarOcoLoJnSd")
            .build();

        // 发起请求
        DownloadFileResp resp = client.drive().v1().file().download(req);

        // 处理服务端错误

        ByteArrayOutputStream data = resp.getData();
        return data;
    }
    private static String getDefaultSheetId(String objToken, Client client) {
        QuerySpreadsheetSheetReq req = QuerySpreadsheetSheetReq
            .newBuilder()
            .spreadsheetToken(objToken)
            .build();

        // 发起请求
        QuerySpreadsheetSheetResp resp = null;
        try {
            resp = client.sheets().v3().spreadsheetSheet().query(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常: {}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }

        // 处理服务端错误
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException(resp.getError().getMessage());
        }
        // 业务数据处理
        com.lark.oapi.service.sheets.v3.model.Sheet[] sheets = resp.getData().getSheets();
        return sheets[0].getSheetId();
    }

    /**
     * 根据电子表格/ 多维表格 URL 获取 appToken
     * @param url 多维表格的 URL
     * @return appToken 或 null
     */
    private static String extractAppTokenFromUrl(String url, Client client) {
        if (url == null || url.isEmpty()) {
            throw new AIPluginException("appToken不能为空");
        }
        // 判断 URL 类型并提取 appToken
        if (url.contains("feishu.cn/base")) {
            return extractBaseAppToken(url);
        } else if (url.contains("feishu.cn/wiki")) {
            // 调用获取知识空间节点信息的 API 获取 appToken
            return fetchAppTokenFromWiki(url, client);
        } else if (url.contains("feishu.cn/sheets")) {
            // 调用获取知识空间节点信息的 API 获取 appToken
            return fetchAppTokenFromSheet(url, client);
        } else {
            return url;
        }
    }

    private static String fetchAppTokenFromSheet(String url, Client client) {
        // 提取 nodeToken
        Pattern pattern = Pattern.compile("/sheet/([\\w-]+)\\?");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            String nodeToken = matcher.group(1);
            // 调用飞书开放平台 API 获取 appToken
            return callWikiApiToGetAppToken(nodeToken, client);
        }
        throw new AIPluginException("无法从 URL 提取 nodeToken");
    }

    /**
     * 提取 base 类型 URL 的 appToken
     * @param url base 类型 URL
     * @return appToken
     */
    private static String extractBaseAppToken(String url) {
        // 使用正则表达式提取 appToken
        Pattern pattern = Pattern.compile("base/([^/]+)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new AIPluginException("无法从 URL 提取 appToken");
    }
    /**
     * 从 wiki 类型 URL 获取 appToken
     * @param url wiki 类型 URL
     * @return appToken
     */
    private static String fetchAppTokenFromWiki(String url, Client client) {
        // 提取 nodeToken
        Pattern pattern = Pattern.compile("/wiki/([\\w-]+)(?:\\?|$)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            String nodeToken = matcher.group(1);
            // 调用飞书开放平台 API 获取 appToken
            return callWikiApiToGetAppToken(nodeToken, client);
        }
        throw new AIPluginException("无法从 URL 提取 nodeToken");
    }
    /**
     * 调用飞书开放平台 API 获取 appToken
     * @param nodeToken 知识空间节点 token
     * @return appToken
     */
    private static String callWikiApiToGetAppToken(String nodeToken, Client client) {
        // 创建请求对象
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
            .token(nodeToken)
            .objType("wiki")
            .build();

        // 发起请求
        try {
            GetNodeSpaceResp resp = client.wiki().v2().space().getNode(req);
            if (!resp.success()) {
                log.error("[FeishuPlugin] Error calling API to get appToken for nodeToken: {}, error: {}", nodeToken, resp.getCode());
                throw new AIPluginException("Error calling API to get appToken: " + resp.getMsg());
            }
            return resp.getData().getNode().getObjToken();
        } catch (Exception e) {
            log.error("[FeishuPlugin] Error calling API to get appToken for nodeToken: {}, error: {}", nodeToken, e.getMessage(), e);
            throw new AIPluginException("Error calling API to get appToken: " + e.getMessage());
        }
    }

    public static void getSheetContent(FSSheetContentQuery query) {
        // 构建请求实体
        String ranges = query.getRanges();
        if(StringUtils.isBlank(ranges)){
            ranges = "A:Z";
        }
        String tenantAccessToken = getFSAccessToken(APP_ID, APP_SECRET);
        String sourceUrl = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/values/%s?valueRenderOption=ToString&dateTimeRenderOption=FormattedString";
        String url = String.format(sourceUrl, query.getObjToken(), query.getSheetId() + "!" + ranges);
        TypeToken<SheetDataResp> typeToken = new TypeToken<>() {
        };

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Authorization", "Bearer " + tenantAccessToken);
        HttpEntity<String> httpEntity = new HttpEntity<>(headers);

        RestTemplate restTemplate = new RestTemplate();
        try {
            ResponseEntity<SheetCellRespBody> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, SheetCellRespBody.class);
            log.info("invoke, result status code: {}", responseEntity.getStatusCode());

            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                log.error("invoke, failed with status code: {}", responseEntity.getStatusCode());
                throw new BusinessException("Failed to invoke API with status code: " + responseEntity.getStatusCode());
            }
            SheetCellRespBody body = responseEntity.getBody();
            SheetDataResp data = body.getData();
//            SheetCellRespBody parse = JsonUtils.parse(responseEntity.getBody(), typeRef);
//            SheetDataResp data = parse.getData();
            System.out.println();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("invoke, error", e);
            throw new BusinessException(e.getMessage());
        }

    }

    private static String getFSAccessToken(String appId, String appSecret) {
        FSTokenRequest request = new FSTokenRequest();
        request.setApp_id(appId);
        request.setApp_secret(appSecret);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        RestTemplate restTemplate = new RestTemplate();
        try {
            String url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.toJson(request), headers);
            ResponseEntity<FSAppTokenResp> responseEntity = restTemplate.postForEntity(url, httpEntity, FSAppTokenResp.class);
            log.info("invoke, result status code: {}", responseEntity.getStatusCode());

            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                log.error("invoke, failed with status code: {}", responseEntity.getStatusCode());
                throw new BusinessException("Failed to invoke API with status code: " + responseEntity.getStatusCode());
            }
            FSAppTokenResp body = responseEntity.getBody();
            return body.getTenantAccessToken();
        } catch (Exception e) {
            log.error("调用飞书接口返回值出错: ", e);
        }
        return StringUtils.EMPTY;
    }

    private static void testMarkStr() throws Exception {
        ListDocumentBlockReq req = ListDocumentBlockReq.newBuilder()
            .documentId("Cw5Id2wWJokMVuxJHlpclsrNnqf")
            .documentRevisionId(-1)
            .build();


        // 发起请求
        ListDocumentBlockResp list = client.docx().v1().documentBlock().list(req);

        // 业务数据处理
//        System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
        String markStr = parse(JsonUtils.toJson(list.getData()), "Cw5Id2wWJokMVuxJHlpclsrNnqf", client);
        System.out.println(markStr);
    }

    public static ListSpaceNodeRespBody getSpaceList(FSSpaceQuery query) {
        if (StringUtils.isEmpty(query.getSpaceId())) {
            Node spaceNode = getSpaceNode(query.getFolderToken(), client);
            String spaceId = spaceNode.getSpaceId();
            String nodeToken = spaceNode.getNodeToken();
            return getSpaceList(spaceId, nodeToken, client);
        }
        return getSpaceList(query.getSpaceId(), query.getNodeToken(), client);
    }

    private static ListSpaceNodeRespBody getSpaceList(String spaceId, String nodeToken, Client client) {
        ListSpaceNodeReq req = ListSpaceNodeReq.newBuilder()
            .spaceId(spaceId)
            .parentNodeToken(nodeToken)
            .build();

        // 发起请求
        ListSpaceNodeResp resp = null;
        try {
            resp = client.wiki().v2().spaceNode().list(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException("调用飞书接口异常: " + e.getMessage());
        }
        // 处理服务端错误
        assert resp != null;
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException(resp.getError().getMessage());
        }
        return resp.getData();
    }

    private static Node getSpaceNode(String folderToken, Client client) {
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
            .token(folderToken)
            .build();

        // 发起请求
        GetNodeSpaceResp resp = null;
        try {
            resp = client.wiki().v2().space().getNode(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException("调用飞书接口异常: " + e.getMessage());
        }

        // 处理服务端错误
        assert resp != null;
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException(resp.getError().getMessage());
        }
        return resp.getData().getNode();
    }

    public static String parse(String response, String documentId, Client client) {
        try {
            tl.set(documentId);
            CLIENT_MAP.put(documentId, client);
            // 读取 JSON 文件
            BlockData parse = JsonUtils.parse(response, BlockData.class);
            List<Block> items = parse.getItems();
            if (CollUtil.isEmpty(items)) {
                return "";
            }
            Block first = items.getFirst();
//            items.remove(first);
            StringBuilder markdown = new StringBuilder();
            TextElement[] elements = first.getPage().getElements();
            TextRun textRun = elements[0].getTextRun();
            // 文档页标题
            markdown.append(textRun.getContent()).append("\n\n");

            Map<String, Block> blockMap = items
                .stream()
                .collect(Collectors.toMap(Block::getBlockId, Function.identity()));
            BLOCK_MAP.putAll(blockMap);
            String[] children = first.getChildren();
            for (String blockId : children) {
                Block block = blockMap.get(blockId);
                processBlock(block, markdown, 1);
            }
            return markdown.toString();
        } catch (Exception e) {

        } finally {
            BLOCK_MAP.clear();
            tl.remove();
        }
        return "";
    }

    private static void processBlock(Block block, StringBuilder markdown, Integer level) {
        if (block == null) {
            return;
        }
        int blockTypeCode = block.getBlockType();
        BlockType blockType = BlockType.getByCode(blockTypeCode);
        if (blockType != null) {
            switch (blockType) {
                case HEADING1:
                    markdown.append(parseHeading(block, 1));
                    break;
                case HEADING2:
                    markdown.append(parseHeading(block, 2));
                    break;
                case HEADING3:
                    markdown.append(parseHeading(block, 3));
                    break;
                case HEADING4:
                    markdown.append(parseHeading(block, 4));
                    break;
                case HEADING5:
                    markdown.append(parseHeading(block, 5));
                    break;
                case HEADING6:
                    markdown.append(parseHeading(block, 6));
                    break;
                case HEADING7:
                    markdown.append(parseHeading(block, 7));
                    break;
                case HEADING8:
                    markdown.append(parseHeading(block, 8));
                    break;
                case HEADING9:
                    markdown.append(parseHeading(block, 9));
                    break;
                case ORDERED:
                    markdown.append(parseOrderedList(block, level));
                    break;
                case BULLET:
                    markdown.append(parseBulletList(block, level));
                    break;
                case TABLE:
                    markdown.append(parseTable(block));
                    break;
                case IMAGE:
                    markdown.append(parseImage(block));
                    break;
                case CODE:
                    markdown.append(parseCodeBlock(block));
                    break;
                case DIVIDER:
                    markdown.append("---\n");
                    break;
                case TEXT:
                    markdown.append(parseText(block));
                    break;
                case TODO:
                    markdown.append(parseTodo(block));
                    break;
                case CALLOUT:
                    markdown.append(parseCallout(block));
                    break;
                case VIEW:
                    markdown.append(parseView(block));
                    break;
                case FILE:
                    markdown.append(parseFile(block));
                    break;
                case QUOTE_CONTAINER:
                    markdown.append("> ");
                    break;
                default:
                    // 其他类型暂不处理
                    break;
            }
        }
        level++;
        // 处理子节点
        String[] children = block.getChildren();
        if (children != null && !BlockType.isTable(blockTypeCode)) {
            // 创建请求对象
            for (String child : children) {
                processBlock(BLOCK_MAP.get(child), markdown, level);
            }
        }
    }

    private static String parseView(Block block) {
        View view = block.getView();
        Integer viewType = view.getViewType();
        if (viewType == 1) {
            // 卡片视图，独占一行
            return "> 卡片视图\n\n";
        }
        return null;
    }

    private static String parseFile(Block block) {
        File file = block.getFile();
        String name = file.getName();
        String token = file.getToken();
        FSDownload.Info downLoadInfo = getDownLoadInfo(token);
        // todo 处理文件
        return "[" + name + "](" + downLoadInfo.getTmpDownloadUrl() + ")\n\n";
    }

    private static String parseHeading(Block block, int level) {
        Text text = (Text) ReflectUtil.getFieldValue(block, "heading" + level);
        StringBuilder headingMarkdown = new StringBuilder();
        for (int i = 0; i < level; i++) {
            headingMarkdown.append("#");
        }
        headingMarkdown.append(" ");
        TextElement[] elements = text.getElements();
        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            String content = textRun.getContent();
            headingMarkdown.append(content);
        }
        headingMarkdown.append("\n\n");
        return headingMarkdown.toString();
    }

    private static String parseOrderedList(Block block, Integer level) {
        Text ordered = block.getOrdered();
        TextElement[] elements = ordered.getElements();
        TextStyle style = ordered.getStyle();
        String sequence = style.getSequence();
        if (StringUtils.equals(sequence, "auto")) {
            AUTO_ORDER.computeIfAbsent(block.getParentId(), k -> 1); // 如果不存在，则设置为 0
            int currentOrder = AUTO_ORDER.get(block.getParentId());
            AUTO_ORDER.put(block.getParentId(), currentOrder + 1); // 更新序号
            sequence = String.valueOf(currentOrder);
        }
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < level - 1; i++) {
            sb.append("    ");
        }
        TextRun textRun = elements[0].getTextRun();
        sb.append(sequence).append(". ").append(textRun.getContent()).append("\n");
        return sb.toString();
    }

    private static String parseBulletList(Block block, Integer level) {
        Text bullet = block.getBullet();
        TextElement[] elements = bullet.getElements();

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < level - 1; i++) {
            sb.append("    ");
        }
        TextRun textRun = elements[0].getTextRun();
        sb.append("- ").append(textRun.getContent()).append("\n");
        return sb.toString();
    }

    private static String parseTable(Block block) {
        Table table = block.getTable();
        TableProperty property = table.getProperty();
        String[] cells = table.getCells();
        int columnSize = property.getColumnSize();
        int rowSize = property.getRowSize();

        StringBuilder tableMarkdown = new StringBuilder();
        // 构建表头
        tableMarkdown.append("|");
        for (int i = 0; i < columnSize; i++) {
            String cellContent = getCellContent(cells[i]);
            tableMarkdown.append(" ").append(cellContent).append(" |");
        }
        tableMarkdown.append("\n");

        // 构建分隔行
        tableMarkdown.append("|");
        for (int i = 0; i < columnSize; i++) {
            tableMarkdown.append(" --- |");
        }
        tableMarkdown.append("\n");

        // 填充数据
        int cellIndex = columnSize; // 从表头之后的数据开始填充
        for (int i = 0; i < rowSize - 1; i++) { // 减去表头行
            tableMarkdown.append("|");
            for (int j = 0; j < columnSize; j++) {
                if (cellIndex < cells.length) {
                    String cellContent = getCellContent(cells[cellIndex]);
                    tableMarkdown.append(" ").append(cellContent).append(" |");
                    cellIndex++;
                } else {
                    tableMarkdown.append("  |");
                }
            }
            tableMarkdown.append("\n");
        }

        tableMarkdown.append("\n");
        return tableMarkdown.toString();
    }

    // 表格字块中解析
    private static String getCellContent(String cellId) {
        StringBuilder contents = new StringBuilder();
        Block block = BLOCK_MAP.get(cellId);
        if (block == null) {
            return "";
        }
        String[] children = block.getChildren();
        for (String child : children) {
            Block childBlock = BLOCK_MAP.get(child);
            if (childBlock == null) {
                continue;
            }
            Text text = childBlock.getText();
            TextElement[] elements = text.getElements();
            for (TextElement element : elements) {
                String content = element.getTextRun().getContent();
                contents.append(content);
            }
        }
        return contents.toString();
    }

    private static String parseImage(Block block) {
        Image image = block.getImage();
        String token = image.getToken();
        FSDownload.Info first = getDownLoadInfo(token);
        // todo 这里假设 token 是图片链接，实际可能需要根据具体情况处理
        return "![图片](" + first.getTmpDownloadUrl() + ")\n\n";
    }

    private static FSDownload.Info getDownLoadInfo(String token) {
        Client client = CLIENT_MAP.get(tl.get());
        // 创建请求对象
        BatchGetTmpDownloadUrlMediaReq req = BatchGetTmpDownloadUrlMediaReq.newBuilder()
            .fileTokens(new String[]{token})
            .build();

        // 发起请求
        BatchGetTmpDownloadUrlMediaResp resp = null;
        try {
            resp = client.drive().v1().media().batchGetTmpDownloadUrl(req);
        } catch (Exception e) {
            log.error("调飞书接口异常", e);
        }

        // 处理服务端错误
        if (!resp.success()) {
            log.warn("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            return null;
        }

        // 业务数据处理
        List<FSDownload> fsDownloads = JsonUtils.parseToList(JsonUtils.toJson(resp.getData()), FSDownload.class);
        List<FSDownload.Info> tmpDownloadUrls = fsDownloads.getFirst().getTmpDownloadUrls();
        return tmpDownloadUrls.getFirst();
    }

    private static String parseCodeBlock(Block block) {
        Text code = block.getCode();
        TextElement[] elements = code.getElements();
        TextRun textRun = elements[0].getTextRun();
        return "```\n" + textRun.getContent() + "\n```\n\n";
    }

    private static String parseText(Block block) {
        Text text = block.getText();
        TextElement[] elements = text.getElements();
        StringBuilder contentBuilder = new StringBuilder();

        for (TextElement element : elements) {
            TextRun textRun = element.getTextRun();
            String content = textRun.getContent();
            TextElementStyle textElementStyle = textRun.getTextElementStyle();
            Link link = textElementStyle.getLink();
            if (textElementStyle.getBold()) {
                content = "__" + content + "__";
            }
            if (link != null) {
                String url = link.getUrl();
                // 解码 URL
                url = java.net.URLDecoder.decode(url, StandardCharsets.UTF_8);
                contentBuilder.append("[").append(content).append("](").append(url).append(")");
            } else {
                contentBuilder.append(content);
            }
            contentBuilder.append(" ");
        }

        return contentBuilder.toString().trim() + "\n\n";
    }

    private static String parseTodo(Block block) {
        Text todo = block.getTodo();
        TextElement[] elements = todo.getElements();
        TextRun textRun = elements[0].getTextRun();
        return "- [ ] " + textRun.getContent() + "\n\n";
    }

    private static String parseCallout(Block block) {
        // 暂未实现具体解析逻辑，可根据需求补充
        return "<| 高亮块内容待解析 |>\n\n";
    }
}
