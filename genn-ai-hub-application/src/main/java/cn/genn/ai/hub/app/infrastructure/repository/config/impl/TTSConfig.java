package cn.genn.ai.hub.app.infrastructure.repository.config.impl;

import cn.genn.ai.hub.app.infrastructure.repository.config.ModelConfig;
import com.alibaba.nacos.common.utils.CollectionUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 语音合成 tts
 */
@Getter
@Setter
@ToString
public class TTSConfig implements ModelConfig {

    // 模型综合价格
    private BigDecimal charsPointsPrice;

    /**
     * 声音角色
     */
    private List<Map<String, String>> voices;

    private Boolean isCustom;
    @Override
    public boolean validate() {
//        return charsPointsPrice!=null;
        return true;
    }
}
