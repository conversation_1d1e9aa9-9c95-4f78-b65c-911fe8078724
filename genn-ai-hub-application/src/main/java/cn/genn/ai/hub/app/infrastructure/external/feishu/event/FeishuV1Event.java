package cn.genn.ai.hub.app.infrastructure.external.feishu.event;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 飞书V1版本事件
 * @date 2025-04-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FeishuV1Event extends FeishuBaseEvent {

    private String ts;
    private String uuid;
    private String token;
    private String type; // 顶层 type（如 "event_callback"）

    // 从 event 中提取具体事件类型
    public String getEventType() {
        return eventType != null ? eventType : getEvent() != null ? getEvent().path("type").asText() : type;
    }

    public String getAppId() {
        return appId != null ? appId : getEvent() != null ? getEvent().path("app_id").asText() : null;
    }

    public String getTenantKey() {
        return tenantKey != null ? tenantKey : getEvent() != null ? getEvent().path("tenant_key").asText() : null;
    }

    public String getEventId() {
        return super.getEventId();
    }

}
