package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.genn.core.model.enums.DeletedTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * KbRepoTaskPO对象
 *
 * <AUTHOR>
 * @desc 知识库任务队列
 */
@Data
@TableName(value = "kb_repo_task", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class KbRepoTaskPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;


    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 知识库ID
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 任务类型, file_parse:FILE_PARSE-文件解析, text_chunk:TEXT_CHUNK-文本分块,index_vector:INDEX_VECTOR索引向量化
     */
    @TableField("task_type")
    private TaskTypeEnum taskType;

    /**
     * 业务键
     */
    @TableField("business_key")
    private String businessKey;

    /**
     * 业务消息体
     */
    @TableField("business_body")
    private String businessBody;

    /**
     * 任务状态, pending:PENDING-排队中,processing:PROCESSING-处理中,completed:COMPLETED-已完成,failed:FAILED-失败
     */
    @TableField("task_status")
    private TaskStatusEnum taskStatus;

    /**
     * 执行次数
     */
    @TableField("execute_count")
    private Integer executeCount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

