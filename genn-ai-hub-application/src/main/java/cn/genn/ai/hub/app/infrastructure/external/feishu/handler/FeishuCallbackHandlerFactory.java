package cn.genn.ai.hub.app.infrastructure.external.feishu.handler;

import cn.genn.ai.hub.app.infrastructure.external.feishu.event.EventCallbackEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 回调工厂
 * @date 2024-12-25
 */
@Component
public class FeishuCallbackHandlerFactory {

    @Resource
    private Map<EventCallbackEnum, FeishuCallbackHandler> callbackHandlerMap;

    public FeishuCallbackHandler getCallbackHandler(EventCallbackEnum event) {
        return callbackHandlerMap.get(event);
    }
}
