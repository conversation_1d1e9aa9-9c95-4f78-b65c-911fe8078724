package cn.genn.ai.hub.app.infrastructure.external.feishu.handler;

import cn.genn.ai.hub.app.application.dto.feishu.EventHandleDTO;
import cn.genn.ai.hub.app.application.enums.feishu.ChatTypeEnum;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.EventCallbackEnum;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.FeishuEventParser;
import cn.genn.ai.hub.app.infrastructure.external.feishu.utils.FeishuMessageSender;
import cn.genn.ai.hub.app.infrastructure.external.feishu.utils.FeishuMessageUtils;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentChannelRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ChannelBaseRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.ChannelBasePO;
import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.ai.hub.core.api.channel.FeishuConfig;
import cn.genn.core.utils.jackson.JsonUtils;
import com.lark.oapi.service.application.v6.enums.MessageTypeEnum;
import com.lark.oapi.service.im.v1.model.MentionEvent;
import com.lark.oapi.service.im.v1.model.P2MessageReceiveV1Data;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 接收事件处理器
 * @date 2025-04-09
 */
@Slf4j
@Component
@AllArgsConstructor
public class ReceiveMessageHandler implements FeishuCallbackHandler {

    private final AgentChannelRepositoryImpl agentChannelRepository;

    private final ChannelBaseRepositoryImpl channelBaseRepository;

    private final FeishuMessageSender feishuMessageSender;

    @Override
    public void handle(EventHandleDTO command) {
        try {
            log.info("[ReceiveMessage] Processing | event: {}", JsonUtils.toJson(command.getEvent()));
            P2MessageReceiveV1Data event = FeishuEventParser.parseEvent(
                command.getEvent(),
                P2MessageReceiveV1Data.class
            );
            ChatTypeEnum chatTypeEnum = ChatTypeEnum.of(event.getMessage().getChatType());
            AgentChannelPO agentChannelPO = agentChannelRepository.queryByChannelUnique(ChannelTypeEnum.FEISHU, command.getAppId());
            if (agentChannelPO == null) {
                log.error("[ReceiveMessage] Agent channel not found for feishu appId: {}", command.getAppId());
                return;
            }

            String userMessage = FeishuMessageUtils.extractUserInput(event);
            // 仅支持文字消息
            if (MessageTypeEnum.TEXT.getValue().equals(event.getMessage().getMessageType())) {
                if (ChatTypeEnum.P2P.equals(chatTypeEnum)) {
                    feishuMessageSender.handleTextMessage(command, chatTypeEnum, agentChannelPO.getWorkflowId(),
                        userMessage, event.getMessage().getMessageId(), event.getMessage().getChatId(),
                        event.getSender().getSenderId().getOpenId());
                } else if (ChatTypeEnum.GROUP.equals(chatTypeEnum)) {
                    if (event.getMessage().getMentions() == null || event.getMessage().getMentions().length == 0) {
                        // 如果没有艾特任何人，则直接返回
                        return;
                    }
                    // 获取消息中的 mentions
                    List<MentionEvent> mentions = Arrays.asList(event.getMessage().getMentions());
                    if (!mentions.isEmpty()) {
                        // 判断是否提及机器人
                        // 查询机器人的open_id
                        ChannelBasePO channelBasePO = channelBaseRepository.queryByChannelTypeAndUniqueKey(ChannelTypeEnum.FEISHU, command.getAppId());
                        FeishuConfig feishuConfig = (FeishuConfig) channelBasePO.getParsedConfig();
                        boolean isMentionedBot = mentions.stream()
                            .anyMatch(mention -> mention.getId().getOpenId().equals(feishuConfig.getBotOpenId()));
                        // 如果提及了机器人，则处理消息
                        if (isMentionedBot) {
                            feishuMessageSender.handleTextMessage(command, chatTypeEnum, agentChannelPO.getWorkflowId(),
                                userMessage, event.getMessage().getMessageId(), event.getMessage().getChatId(),
                                event.getSender().getSenderId().getOpenId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[ReceiveMessage] Processing failed | event:{}", JsonUtils.toJson(command.getEvent()), e);
        }
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.RECEIVE_MESSAGE;
    }
}
