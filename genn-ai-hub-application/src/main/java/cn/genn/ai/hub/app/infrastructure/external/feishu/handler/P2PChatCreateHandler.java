package cn.genn.ai.hub.app.infrastructure.external.feishu.handler;

import cn.genn.ai.hub.app.application.dto.feishu.EventHandleDTO;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.infrastructure.external.agent.AgentInvokeService;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.EventCallbackEnum;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.FeishuEventParser;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.FeishuP2pChatCreateEvent;
import cn.genn.ai.hub.app.infrastructure.external.feishu.utils.FeishuMessageUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description 用户和机器人的会话首次被创建事件处理
 * @date 2025-04-09
 */
@Slf4j
@Component
@AllArgsConstructor
public class P2PChatCreateHandler implements FeishuCallbackHandler {

    private final FeishuClientService feishuClientService;

    private final AgentInvokeService agentInvokeService;

    private final Executor virtualThreadExecutor;

    @Override
    public void handle(EventHandleDTO command) {
        log.info("[P2PChatCreate] Processing | event: {}", JsonUtils.toJson(command));
        FeishuP2pChatCreateEvent event = FeishuEventParser.parseEvent(
            command.getEvent(),
            FeishuP2pChatCreateEvent.class
        );
        try {
            String welcomeText = agentInvokeService.invokeAgentInitChat(command.getAppId(), event.getChatId());
            if (StrUtil.isNotEmpty(welcomeText)) {
                sendWelcomeMessage(command, event.getChatId(), welcomeText);
            }
        } catch (Exception e) {
            log.error("[P2PChatCreate] Processing failed | event: {}", JsonUtils.toJson(command), e);
        }
    }

    private void sendWelcomeMessage(EventHandleDTO command, String chatId, String welcomeText) {
        virtualThreadExecutor.execute(() -> {
            try {
                Map<String, String> content = Map.of("text", welcomeText);
                CreateMessageReq request = FeishuMessageUtils.buildMessageRequest(chatId, content);
                CreateMessageResp response = feishuClientService.getClient(command.getAppId())
                    .im().message().create(request);
                FeishuMessageUtils.logMessageResponse(chatId, response);
            } catch (Exception e) {
                log.error("[P2PChatCreate] Message send failed | chatId:{}", chatId, e);
            }
        });
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.P2P_CHAT_CREATE;
    }
}
