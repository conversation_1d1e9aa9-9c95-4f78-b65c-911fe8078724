package cn.genn.ai.hub.app.infrastructure.config;

import cn.genn.ai.hub.app.infrastructure.repository.config.impl.LLMConfig;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ModelManageRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelManagePO;
import cn.genn.spring.boot.starter.ai.component.GennAIPropertiesCustomizer;
import cn.genn.spring.boot.starter.ai.constant.AIProviderType;
import cn.genn.spring.boot.starter.ai.properties.AIProviderProperties;
import cn.genn.spring.boot.starter.ai.properties.GennAIProperties;
import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class GennAIPropertiesProvider implements GennAIPropertiesCustomizer {

    @Resource
    private ModelManageRepositoryImpl manageRepository;

    @Override
    public void customize(GennAIProperties properties) {
        List<ModelManagePO> activeMode = manageRepository.getActives();
        if (CollUtil.isEmpty(activeMode)) {
            return;
        }
        activeMode.forEach((model) -> {
            AIProviderProperties aiProviderProperties = new AIProviderProperties();
            aiProviderProperties.setModelId(Long.toString(model.getId()));
            aiProviderProperties.setProviderType(AIProviderType.OPENAI);
            aiProviderProperties.setBaseUrl(model.getBaseUrl());
            aiProviderProperties.setApiKey(model.getApiKey());
            aiProviderProperties.setModelKey(model.getName());
            aiProviderProperties.setModel(model.getModel());
            switch (model.getType()) {
                case LLM -> {
                    aiProviderProperties.setCompletionsPath(model.getApiPath());
                    LLMConfig llmConfig = (LLMConfig) model.getCommonConfig();
                    aiProviderProperties.setVision(llmConfig.getVision() != null && llmConfig.getVision());
                    aiProviderProperties.setToolChoice(llmConfig.getToolChoice() != null && llmConfig.getToolChoice());
                    aiProviderProperties.setReasoning(llmConfig.getReasoning() != null && llmConfig.getReasoning());
                    aiProviderProperties.setFunctionCall(llmConfig.getFunctionCall() != null && llmConfig.getFunctionCall());
                    aiProviderProperties.setMaxTokens(llmConfig.getMaxResponse());
                }
                case INDEX -> aiProviderProperties.setEmbeddingsPath(model.getApiPath());
                case RERANK -> aiProviderProperties.setRerankPath(model.getApiPath());
            }
            properties.getProviders().add(aiProviderProperties);
        });
    }
}
