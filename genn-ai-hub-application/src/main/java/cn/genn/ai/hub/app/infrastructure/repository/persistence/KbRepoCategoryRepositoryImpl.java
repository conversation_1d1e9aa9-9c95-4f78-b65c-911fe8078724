package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoCategoryAssembler;
import cn.genn.ai.hub.app.application.assembler.ModelManageAssembler;
import cn.genn.ai.hub.app.application.dto.KbCatPath;
import cn.genn.ai.hub.app.application.dto.KbRepoCatDetailDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoCategoryDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCatDetailEditCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCatDetailQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCategoryMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ModelManageMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCategoryPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Date: 2025/3/7
 * @Author: kangjian
 */
@Repository
public class KbRepoCategoryRepositoryImpl extends ServiceImpl<KbRepoCategoryMapper, KbRepoCategoryPO> {

    @Resource
    private KbRepoCategoryMapper categoryMapper;
    @Resource
    private KbRepoCategoryAssembler assembler;

    /**
     * 按父目录查询所有的目录结构
     *
     * @param query
     * @return
     */
    public List<KbRepoCategoryDTO> getKbRepoCatDetail(KbRepoCatDetailQuery query) {
        QueryWrapper<KbRepoCategoryPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoCategoryPO::getPid, query.getCatId())
            .like(StrUtil.isNotBlank(query.getName()), KbRepoCategoryPO::getCatName, query.getName())
            .eq(KbRepoCategoryPO::getDeleted, DeletedEnum.NOT_DELETED);
        List<KbRepoCategoryDTO> dtoList = assembler.PO2DTO(categoryMapper.selectList(queryWrapper));
        if (CollUtil.isEmpty(dtoList)) {
            return dtoList;
        }
        // 如果存在父目录,需要赋值path路径的信息
        Set<Long> parentIds = dtoList.stream().map(KbRepoCategoryDTO::getPid).collect(Collectors.toSet());
        parentIds.remove(0L);
        if (CollUtil.isNotEmpty(parentIds)) {
            // 查询全部目录 构造
            QueryWrapper<KbRepoCategoryPO> queryParentWrapper = new QueryWrapper<>();
            queryParentWrapper.lambda()
                .eq(KbRepoCategoryPO::getDeleted, DeletedEnum.NOT_DELETED);
            List<KbRepoCategoryPO> allCategoryPOs = categoryMapper.selectList(queryParentWrapper);
            Map<Long, KbRepoCategoryPO> allCategoryPOsMap = allCategoryPOs.stream().collect(Collectors.toMap(KbRepoCategoryPO::getId, Function.identity()));
            for (KbRepoCategoryDTO dto : dtoList) {
                List<KbCatPath> catPaths = new ArrayList<>();
                Long currentPid = dto.getPid();
                while (currentPid != null && currentPid != 0L) {
                    KbRepoCategoryPO parentPO = allCategoryPOsMap.get(currentPid);
                    if (parentPO != null) {
                        KbCatPath catPath = KbCatPath
                            .builder()
                            .catId(parentPO.getId())
                            .catName(parentPO.getCatName())
                            .build();
                        catPaths.add(catPath);
                        currentPid = parentPO.getPid();
                    } else {
                        break;
                    }
                }
                dto.setCatPath(catPaths);
            }
        }
        return dtoList;
    }

    public List<KbRepoCategoryDTO> getByPId(Long catId) {
        QueryWrapper<KbRepoCategoryPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoCategoryPO::getPid, catId)
            .eq(KbRepoCategoryPO::getDeleted, DeletedEnum.NOT_DELETED);
        return assembler.PO2DTO(categoryMapper.selectList(queryWrapper));
    }

    public Boolean deleteById(Long catId) {
        UpdateWrapper<KbRepoCategoryPO> update = new UpdateWrapper<>();
        update.lambda()
            .eq(KbRepoCategoryPO::getId, catId)
            .set(KbRepoCategoryPO::getDeleted, DeletedEnum.DELETED);
        return update(update);
    }

    public Boolean editCatDetail(KbRepoCatDetailEditCommand command) {
        UpdateWrapper<KbRepoCategoryPO> update = new UpdateWrapper<>();
        update.lambda()
            .eq(KbRepoCategoryPO::getId, command.getCatId())
            .set(KbRepoCategoryPO::getCatName, command.getCatName())
            .set(KbRepoCategoryPO::getDescription, command.getDescription());
        return update(update);
    }

    public boolean moveCat(Long curId, Long tagCatId) {
        UpdateWrapper<KbRepoCategoryPO> update = new UpdateWrapper<>();
        update.lambda()
            .eq(KbRepoCategoryPO::getId, curId)
            .set(KbRepoCategoryPO::getPid, tagCatId);
        return update(update);
    }
}
