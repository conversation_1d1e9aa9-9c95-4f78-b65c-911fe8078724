package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoCasesAssembler;
import cn.genn.ai.hub.app.application.command.KbRepoCasesCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoCasesDTO;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.query.KbRepoCasesQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCasesMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCasesPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 运单仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class CasesRepositoryImpl extends ServiceImpl<KbRepoCasesMapper, KbRepoCasesPO> {

    private final KbRepoCasesMapper mapper;
    private final KbRepoCasesAssembler assembler;


    public PageResultDTO<KbRepoCasesDTO> page(KbRepoCasesQuery query) {
        QueryWrapper<KbRepoCasesPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoCasesPO::getRepoId, query.getRepoId())
            .eq(StrUtil.isNotBlank(query.getTag()), KbRepoCasesPO::getTag, query.getTag())
            .eq(StrUtil.isNotBlank(query.getCategory()), KbRepoCasesPO::getCategory, query.getCategory())
            // 名称模糊查询
            .like(StrUtil.isNotBlank(query.getTitle()), KbRepoCasesPO::getTitle, query.getTitle())
            .orderByDesc(KbRepoCasesPO::getId)
        ;
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
    }

    @IgnoreTenant
    public KbRepoCasesDTO selectById(Long id) {
        return assembler.PO2DTO(getById(id));
    }

    public Long create(KbRepoCasesCommand command) {
        KbRepoCasesPO po = assembler.convertPO(command);
        mapper.insert(po);
        return po.getId();
    }

    public List<KbRepoCasesDTO> batchCreate(List<KbRepoCasesCommand> commands) {
        List<KbRepoCasesPO> pos = assembler.convertPOs(commands);
        mapper.insert(pos);
        return assembler.PO2DTO(pos);
    }

    public void edit(KbRepoCasesCommand command) {
        KbRepoCasesPO po = assembler.convertPO(command);
        mapper.updateById(po);
    }

    public void deleteById(Long id) {
        batchDelete(Lists.newArrayList(id));
    }

    public void batchDelete(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        UpdateWrapper<KbRepoCasesPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .in(KbRepoCasesPO::getId, ids)
            .set(KbRepoCasesPO::getDeleted, DeletedEnum.DELETED);
        mapper.update(updateWrapper);
    }

    @IgnoreTenant
    public void updateCasesHandleStatus(Long casesId, HandleStatusEnum status) {
        UpdateWrapper<KbRepoCasesPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoCasesPO::getId, casesId)
            .set(KbRepoCasesPO::getHandleStatus, status);
        mapper.update(updateWrapper);
    }
}
