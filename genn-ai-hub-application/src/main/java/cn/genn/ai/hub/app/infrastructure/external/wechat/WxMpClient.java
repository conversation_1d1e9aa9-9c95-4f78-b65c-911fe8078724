package cn.genn.ai.hub.app.infrastructure.external.wechat;

import cn.genn.ai.hub.app.infrastructure.constant.WxConstants;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 微信公众号API调用与token缓存
 * @date 2025-04-07
 */
@Component
@AllArgsConstructor
public class WxMpClient {

    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 获取access_token
     *
     * @param appId 公众号appid
     * @param secret 公众号appSecret
     * @param isForceRefresh 是否强制刷新
     * @return access_token
     */
    public String getAccessToken(String appId, String secret, Boolean isForceRefresh) {
        // 1.从redis获取access_token
        // 2.如果没有或者强制刷新，调用微信接口获取access_token
        // 3.存入redis

        if (!isForceRefresh) {
            // 1.从redis获取access_token
            String accessToken = stringRedisTemplate.opsForValue().get(WxConstants.WX_MP_ACCESS_TOKEN_KEY + ":" + appId);
            if (accessToken != null) {
                return accessToken;
            } else {

            }
        }
        return "success";
    }
}
