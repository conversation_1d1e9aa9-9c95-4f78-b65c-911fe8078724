package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.dto.feishu.FSAppConfig;
import cn.genn.ai.hub.app.application.enums.RepoStatusEnum;
import cn.genn.ai.hub.app.application.enums.RepoTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * KbRepoBaseInfoPO对象
 *
 * <AUTHOR>
 * @desc 知识库基础信息
 */
@Data
@TableName(value = "kb_repo_base_info", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class KbRepoBaseInfoPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 目录id
     */
    @TableField("cat_id")
    private Long catId;

    /**
     * 团队id，如果为空，代表是个人空间创建
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * 知识库名称
     */
    @TableField("name")
    private String name;

    /**
     * 知识库图标，支持自定义图标 URL
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 向量模型--对应模型管理的name
     */
    @TableField("vector_model_key")
    private String vectorModelKey;

    /**
     * 文本识别模型--对应模型管理的name
     */
    @TableField("agent_model_key")
    private String agentModelKey;

    /**
     * 图片识别模型名称--对应模型管理的name
     */
    @TableField("img_model_key")
    private String imgModelKey;

    /**
     * 简介，知识库的功能描述
     */
    @TableField("description")
    private String description;

    /**
     * 知识库类型, repo:REPO-通用知识库, website:WEBSITE-网址, feishu:FEISHU-飞书,yuque:YUQUE-语雀
     */
    @TableField("repo_type")
    private RepoTypeEnum repoType;

    /**
     * 状态, active:ACTIVE-激活, syncing:SYNCING-同步中, error:ERROR-错误
     */
    @TableField("repo_status")
    private RepoStatusEnum repoStatus;

    /**
     * 应用渠道ID
     */
    @TableField("channel_id")
    private Long channelId;

    /**
     * 关联
     */
    @TableField("channel_ref_id")
    private Long channelRefId;

    /**
     * 网站配置（仅网站型知识库有效），包含 url, selector
     */
    @TableField("website_config")
    private String websiteConfig;

    /**
     * API 文件服务器配置，用于第三方 API 文件接入
     */
    @TableField("api_server_config")
    private String apiServerConfig;

    /**
     * 飞书服务器配置，包含飞书应用凭证和同步参数
     */
    @TableField(value = "feishu_server_config", typeHandler = JacksonTypeHandler.class)
    private FSAppConfig feishuServerConfig;

    /**
     * 自动同步开关，控制是否定时同步外部数据源（默认 false）
     */
    @TableField("auto_sync")
    private Boolean autoSync;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

