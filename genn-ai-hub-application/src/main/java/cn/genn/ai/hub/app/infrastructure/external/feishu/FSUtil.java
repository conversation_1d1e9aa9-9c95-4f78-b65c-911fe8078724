package cn.genn.ai.hub.app.infrastructure.external.feishu;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.drive.v1.model.DownloadFileReq;
import com.lark.oapi.service.drive.v1.model.DownloadFileResp;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceReq;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class FSUtil {

    private static final Logger log = LoggerFactory.getLogger(FSUtil.class);

    /**
     * 根据 文档/ 电子表格 / 多维表格 URL 获取 tokenUrl
     *
     * @param tokenUrl 文档URL 或者 截取的 token
     * @return appToken 或 null
     */
    public static String getFolderToken(String tokenUrl) {
        if (tokenUrl == null || tokenUrl.isEmpty()) {
            throw new BusinessException("文档地址不能为空");
        }
        // 判断 URL 类型并提取 tokenUrl
        if (tokenUrl.contains("feishu.cn/base")) {
            return getBaseFolderToken(tokenUrl);
        } else if (tokenUrl.contains("feishu.cn/wiki")) {
            // 调用获取知识空间节点信息的 API 获取 tokenUrl
            return getWikiNodeToken(tokenUrl);
        } else if (tokenUrl.contains("feishu.cn/sheets")) {
            // 调用获取知识空间节点信息的 API 获取 tokenUrl
            return getSheetNodeToken(tokenUrl);
        } else {
            throw new BusinessException("无法识别飞书文档地址");
        }
    }

    public static String getBaseUrl(String tokenUrl) {
        if (tokenUrl == null || tokenUrl.isEmpty()) {
            throw new BusinessException("无法识别飞书文档地址");
        }
        try {
            URI uri = new URI(tokenUrl);
            String scheme = uri.getScheme();
            String host = uri.getHost();
            String path = uri.getPath();

            // 提取基础路径
            String[] pathSegments = path.split("/");
            StringBuilder baseUrl = new StringBuilder();
            baseUrl.append(scheme).append("://").append(host);

            for (String segment : pathSegments) {
                if (segment.isEmpty()) {
                    continue;
                }
                baseUrl.append("/").append(segment);
                if (segment.equals("base") || segment.equals("wiki") || segment.equals("sheets") || segment.equals("docx")) {
                    break;
                }
            }

            // 确保以斜杠结尾
            if (!baseUrl.toString().endsWith("/")) {
                baseUrl.append("/");
            }

            return baseUrl.toString();
        } catch (Exception e) {
            log.error("无效的 URL: {}", tokenUrl, e);
            throw new BusinessException("文档地址无效: " + tokenUrl);
        }
    }

    /**
     * 调用飞书开放平台 API 获取 nodeToken
     *
     * @param folderToken 知识空间节点 token
     * @return nodeToken
     */
    public static String getFSNodeToken(String folderToken, Client client) {
        // 创建请求对象
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
            .token(folderToken)
            .build();

        // 发起请求
        try {
            GetNodeSpaceResp resp = client.wiki().v2().space().getNode(req);
            if (!resp.success()) {
                log.error("[FS client] Error calling API to get docx folderToken: {}, error: {}", folderToken, resp.getCode());
                throw new BusinessException("调用飞书接口失败: " + resp.getMsg());
            }
            return resp.getData().getNode().getObjToken();
        } catch (Exception e) {
            log.error("[FS client] Error calling API to get docx folderToken: {}, error: {}", folderToken, e.getMessage(), e);
            throw new BusinessException("调用飞书接口异常: " + e.getMessage());
        }
    }

    /**
     * 提取 sheets 类型 URL 的 folderToken
     *
     * @param url sheets 类型 URL
     * @return folderToken
     */
    private static String getSheetNodeToken(String url) {
        // 提取 nodeToken
        Pattern pattern = Pattern.compile("/sheets/([\\w-]+)(?:\\?|$)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new BusinessException("无法识别飞书文档有效token");
    }

    /**
     * 提取 base 类型 URL 的 folderToken
     *
     * @param url base 类型 URL
     * @return folderToken
     */
    private static String getBaseFolderToken(String url) {
        // 使用正则表达式提取 appToken
        Pattern pattern = Pattern.compile("base/([^/]+)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new BusinessException("无法识别飞书文档有效token");
    }

    /**
     * 从 wiki 类型 URL 获取 folderToken
     *
     * @param url wiki 类型 URL
     * @return folderToken
     */
    private static String getWikiNodeToken(String url) {
        // 提取 nodeToken
        Pattern pattern = Pattern.compile("/wiki/([\\w-]+)(?:\\?|$)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);

        }
        throw new BusinessException("无法识别飞书文档有效token");
    }

    public static String toExcelColumnName(int column) {
        StringBuilder columnName = new StringBuilder();
        while (column >= 0) {
            int remainder = column % 26;
            char letter = (char) ('A' + remainder);
            columnName.insert(0, letter);
            column = (column - remainder) / 26 - 1;
        }
        return columnName.toString();
    }


    public static List<String> getSheetMarkTable(List<String> headers, List<Map<Integer, String>> cachedDataList, Integer chunckSize) {
        // 提取表头
        if (null == headers || headers.isEmpty()) {
            headers = Lists.newArrayList(cachedDataList.getFirst().values());
            headers = headers.stream().map(value -> value == null ? "" : value).collect(Collectors.toList());
            cachedDataList.removeFirst();
        }
        // 将每行数据转换为Map key为表头名称 value为每行的数据
        return convertToMarkdownTable(headers, cachedDataList, chunckSize);
    }


    /**
     * 将表格每行数据转换为Map key为表头名称 value为每行的数据，并转为Markdown表格
     */
    private static List<String> convertToMarkdownTable(List<String> headers, List<Map<Integer, String>> cachedDataList, Integer chunckSize) {
        List<String> result = new ArrayList<>();
        if (chunckSize == null) {
            chunckSize = cachedDataList.size();
        }
        // 按分块大小处理数据
        for (int i = 0; i < cachedDataList.size(); i += chunckSize) {
            StringBuilder markdownTable = new StringBuilder();
            // 添加Markdown表头
            markdownTable.append("|").append(String.join("|", headers)).append("|\n");
            // 添加分隔符行
            markdownTable.append("|").append(String.join("|", Collections.nCopies(headers.size(), "---"))).append("|\n");
            // 获取当前分块的数据
            List<Map<Integer, String>> chunk = cachedDataList.subList(i, Math.min(i + chunckSize, cachedDataList.size()));
            for (Map<Integer, String> rowData : chunk) {
                List<String> rowValues = new ArrayList<>();
                for (int j = 0; j < headers.size(); j++) {
                    rowValues.add(rowData.getOrDefault(j, ""));
                }
                rowValues = rowValues.stream().map(value -> value == null ? "" : value).collect(Collectors.toList());
                markdownTable.append("|").append(String.join("|", rowValues)).append("|\n");
            }
            result.add(markdownTable.toString());
        }
        return result;
    }

    public static String convertToMarkdownTable(List<List<Object>> values) {
        if (CollUtil.isEmpty(values)) {
            return "";
        }

        StringBuilder markdownTable = new StringBuilder();

        // 提取表头
        List<Object> headerRow = values.get(0);
        int columnCount = headerRow.size();

        // 构建表头行
        markdownTable.append("|");
        for (Object header : headerRow) {
            markdownTable.append(" ").append(header == null ? "" : header.toString()).append(" |");
        }
        markdownTable.append("\n");

        // 构建分隔行
        markdownTable.append("|");
        for (int i = 0; i < columnCount; i++) {
            markdownTable.append(" --- |");
        }
        markdownTable.append("\n");

        // 构建数据行
        for (int i = 1; i < values.size(); i++) {
            List<Object> dataRow = values.get(i);
            markdownTable.append("|");
            for (Object cell : dataRow) {
                markdownTable.append(" ").append(cell == null ? "" : cell.toString()).append(" |");
            }
            markdownTable.append("\n");
        }

        return markdownTable.toString();
    }


}
