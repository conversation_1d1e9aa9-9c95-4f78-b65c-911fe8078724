package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class FSLoginProperties {
    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用s
     */
    private String appSecret;

    /**
     * 重定向地址
     */
    private String redirectUri;

    /**
     * 租户
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 默认密码
     */
    private String password;

    private Boolean checkState = true;
    /**
     * 默认角色
     */
    private List<Long> roleIdList;
}
