package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroup;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpGroupRepository;
import cn.genn.ai.hub.app.infrastructure.converter.McpGroupConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.McpGroupMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpGroupPO;
import cn.genn.core.model.enums.BooleanTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * MCP分组仓储实现
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class McpGroupRepositoryImpl extends ServiceImpl<McpGroupMapper, McpGroupPO> implements IMcpGroupRepository {

    private final McpGroupConverter mcpGroupConverter;

    @Override
    public boolean save(McpGroup mcpGroup) {
        McpGroupPO mcpGroupPO = mcpGroupConverter.toMcpGroupPO(mcpGroup);
        boolean result = super.save(mcpGroupPO);
        if (result) {
            mcpGroup.setId(mcpGroupPO.getId());
        }
        return result;
    }

    @Override
    public McpGroup getById(Long id) {
        McpGroupPO mcpGroupPO = super.getById(id);
        return mcpGroupPO != null ? mcpGroupConverter.toMcpGroup(mcpGroupPO) : null;
    }

    @Override
    public McpGroup getByGroupKey(String groupKey) {
        LambdaQueryWrapper<McpGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McpGroupPO::getGroupKey, groupKey);
        McpGroupPO mcpGroupPO = super.getOne(queryWrapper);
        return mcpGroupPO != null ? mcpGroupConverter.toMcpGroup(mcpGroupPO) : null;
    }

    @Override
    public boolean updateById(McpGroup mcpGroup) {
        McpGroupPO mcpGroupPO = mcpGroupConverter.toMcpGroupPO(mcpGroup);
        return super.updateById(mcpGroupPO);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }


    @Override
    public void publishMcpGroup(Long id) {
        McpGroupPO mcpGroupPO = new McpGroupPO();
        mcpGroupPO.setId(id);
        mcpGroupPO.setPublish(BooleanTypeEnum.TRUE);
        super.updateById(mcpGroupPO);
    }

    @Override
    public void unpublishMcpGroup(Long id) {
        McpGroupPO mcpGroupPO = new McpGroupPO();
        mcpGroupPO.setId(id);
        mcpGroupPO.setPublish(BooleanTypeEnum.FALSE);
        super.updateById(mcpGroupPO);
    }

    @Override
    public List<McpGroup> batchGetByIds(Set<Long> groupIds) {
        LambdaQueryWrapper<McpGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(McpGroupPO::getId, groupIds);
        List<McpGroupPO> mcpGroupPOs = super.list(queryWrapper);
        return mcpGroupConverter.toMcpGroup(mcpGroupPOs);
    }


}
