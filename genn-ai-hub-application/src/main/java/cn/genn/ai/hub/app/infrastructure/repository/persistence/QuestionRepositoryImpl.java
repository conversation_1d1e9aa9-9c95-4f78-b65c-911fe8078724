package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.QuestionInfoAssembler;
import cn.genn.ai.hub.app.application.command.QuestionInfoCommand;
import cn.genn.ai.hub.app.application.dto.question.HighFreAnalysisInfo;
import cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO;
import cn.genn.ai.hub.app.application.query.QuestionInfoQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionInfoPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.FsDepartmentDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 运单仓储实现
 *
 * <AUTHOR>
 */
@Repository
@IgnoreTenant
@RequiredArgsConstructor
public class QuestionRepositoryImpl extends ServiceImpl<QuestionInfoMapper, QuestionInfoPO> {

    private final AgentInfoRepositoryImpl agentInfoRepository;

    private final QuestionInfoMapper infoMapper;
    private final QuestionInfoAssembler questionInfoAssembler;
    private final IUpmUserService upmUserService;

    @IgnoreTenant
    public List<QuestionInfoDTO> infos(QuestionInfoQuery query) {
        LambdaQueryWrapper<QuestionInfoPO> wrapper = new QueryWrapper<QuestionInfoPO>()
            .lambda()
            .in(CollUtil.isNotEmpty(query.getIds()), QuestionInfoPO::getId, query.getIds())
            .eq(StringUtils.isNotEmpty(query.getAppId()), QuestionInfoPO::getAppId, query.getAppId())
            .eq(StringUtils.isNotEmpty(query.getChatId()), QuestionInfoPO::getChatId, query.getChatId())
            .eq(StringUtils.isNotEmpty(query.getTaskId()), QuestionInfoPO::getTaskId, query.getTaskId())
            .eq(StringUtils.isNotEmpty(query.getSource()), QuestionInfoPO::getSource, query.getSource())
            .orderByDesc(QuestionInfoPO::getId);
        return questionInfoAssembler.PO2DTO(infoMapper.selectList(wrapper));
    }

    public Long add(QuestionInfoCommand command) {
        QuestionInfoPO po = questionInfoAssembler.convertPO(command);
        Long tenantId = agentInfoRepository.getTenantIdByWorkflowId(command.getAppId());
        po.setTenantId(tenantId);
        po.setDate(LocalDate.now());

        //获取用户数据
        UpmUserQuery upmUserQuery = new UpmUserQuery();
        upmUserQuery.setUserIdList(Collections.singletonList(po.getUserId()));
        List<UpmUserDTO> upmUsers = upmUserService.conditionList(upmUserQuery);
        if(CollUtil.isNotEmpty(upmUsers)){
            UpmUserDTO upmUserDTO = upmUsers.getFirst();
            if(ObjUtil.isNotNull(upmUserDTO.getFsUser()) && CollUtil.isNotEmpty(upmUserDTO.getFsUser().getFsDepartments())){
                List<FsDepartmentDTO> fsDepartments = upmUserDTO.getFsUser().getFsDepartments();
                po.setFirstDepartment(fsDepartments.getLast().getName());
                po.setLastDepartment(fsDepartments.getFirst().getName());
            }
        }
        infoMapper.insert(po);
        return po.getId();
    }

    @IgnoreTenant
    public List<HighFreAnalysisInfo> getListByAppIdAndSource(String appId, String source) {
        return infoMapper.getListByAppIdAndSource(appId, source);
    }

    @IgnoreTenant
    public QuestionInfoDTO queryById(Serializable id) {
        return questionInfoAssembler.PO2DTO(getById(id));
    }

    @IgnoreTenant
    public List<QuestionInfoDTO> getQuestionByAppId(String appId, LocalDate left, LocalDate right) {
        LambdaQueryWrapper<QuestionInfoPO> wrapper = new QueryWrapper<QuestionInfoPO>()
            .lambda()
            .eq(QuestionInfoPO::getAppId, appId)
            .ge(QuestionInfoPO::getDate, left)
            .le(QuestionInfoPO::getDate, right);
        return questionInfoAssembler.PO2DTO(infoMapper.selectList(wrapper));
    }

    @IgnoreTenant
    public List<QuestionInfoDTO> getAllAnalysisInfo(String appId) {
        return infoMapper.getAllAnalysisInfo(appId);
    }



}
