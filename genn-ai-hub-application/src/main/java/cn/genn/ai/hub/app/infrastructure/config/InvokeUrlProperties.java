package cn.genn.ai.hub.app.infrastructure.config;

import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class InvokeUrlProperties {


    /**
     * 请求接口路径
     */
    private String baseUrl;

    /**
     * 查询接口路径
     */
    private String searchPath;
    /**
     * 实时解析文件内容接口
     */
    private String realtimeParseFilePath;
    private String realtimeParseFileChunkPath;

    private String deleteDataIndexVectorOfIndexPath;
    private String deleteDataIndexVectorOfRepoPath;
    private String deleteDataIndexVectorOfCollectionPath;
    private String deleteDataIndexVectorOfDataPath;

    private String urlToImagePath = "/api/tools/htmlConverter/urlToImage";
    private String urlToPdfPath;


    /**
     * 请求接口路径
     */
    private Map<TaskTypeEnum, String> taskProcessPath;

    public String getRequestUrl(TaskTypeEnum type) {
        return taskProcessPath.get(type) != null ? baseUrl + taskProcessPath.get(type) : "";
    }

    public String getSearchUrl() {
        return baseUrl + searchPath;
    }

    public String getRealtimeParseFileUrl() {
        return baseUrl + realtimeParseFilePath;
    }

    public String getRealtimeParseFileChunkPath() {
        return baseUrl + realtimeParseFileChunkPath;
    }

    public String getDeleteDataIndexVectorOfIndexUrl() {
        return baseUrl + deleteDataIndexVectorOfIndexPath;
    }

    public String getDeleteDataIndexVectorOfRepoUrl() {
        return baseUrl + deleteDataIndexVectorOfRepoPath;
    }

    public String getDeleteDataIndexVectorOfCollectionUrl() {
        return baseUrl + deleteDataIndexVectorOfCollectionPath;
    }

    public String getDeleteDataIndexVectorOfDataUrl() {
        return baseUrl + deleteDataIndexVectorOfDataPath;
    }

}
