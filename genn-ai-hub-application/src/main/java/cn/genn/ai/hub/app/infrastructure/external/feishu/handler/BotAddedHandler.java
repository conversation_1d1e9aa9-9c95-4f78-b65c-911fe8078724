package cn.genn.ai.hub.app.infrastructure.external.feishu.handler;

import cn.genn.ai.hub.app.application.dto.feishu.EventHandleDTO;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.infrastructure.external.agent.AgentInvokeService;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.EventCallbackEnum;
import cn.genn.ai.hub.app.infrastructure.external.feishu.event.FeishuEventParser;
import cn.genn.ai.hub.app.infrastructure.external.feishu.utils.FeishuMessageUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.lark.oapi.service.im.v1.model.P2ChatMemberBotAddedV1Data;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description 机器人进群处理器
 * @date 2025-04-09
 */
@Slf4j
@Component
@AllArgsConstructor
public class BotAddedHandler implements FeishuCallbackHandler {

    private final AgentInvokeService agentInvokeService;

    private final FeishuClientService feishuClientService;

    private final Executor virtualThreadExecutor;

    @Override
    public void handle(EventHandleDTO command) {
        log.info("[BotAdded] Processing | event: {}", JsonUtils.toJson(command.getEvent()));
        P2ChatMemberBotAddedV1Data event = FeishuEventParser.parseEvent(
            command.getEvent(),
            P2ChatMemberBotAddedV1Data.class
        );
        try {
            String chatId = event.getChatId();
            String welcomeText = agentInvokeService.invokeAgentInitChat(command.getAppId(), chatId);

            if (StrUtil.isNotEmpty(welcomeText)) {
                sendWelcomeMessage(command, chatId, welcomeText);
            }
        } catch (Exception e) {
            log.error("[BotAdded] Processing failed | event: {}", JsonUtils.toJson(command.getEvent()), e);
        }
    }

    private void sendWelcomeMessage(EventHandleDTO command, String chatId, String welcomeText) {
        virtualThreadExecutor.execute(() -> {
            try {
                Map<String, String> content = Map.of("text", welcomeText);
                CreateMessageReq request = FeishuMessageUtils.buildMessageRequest(chatId, content);
                CreateMessageResp response = feishuClientService.getClient(command.getAppId())
                    .im().message().create(request);
                FeishuMessageUtils.logMessageResponse(chatId, response);
            } catch (Exception e) {
                log.error("[BotAdded] Message send failed | chatId:{}", chatId, e);
            }
        });
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.BOT_ADD;
    }
}
