package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.AgentInfoAssembler;
import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.dto.VerifyAgentDTO;
import cn.genn.ai.hub.app.application.enums.AgentTypeEnum;
import cn.genn.ai.hub.app.application.query.AgentInfoQuery;
import cn.genn.ai.hub.app.infrastructure.config.auth.AuthSqlInject;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentInfoPO;
import cn.genn.cache.redis.annotation.Cache;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static cn.genn.ai.hub.app.infrastructure.constant.CacheConstants.AGENT_TENANT_ID_CACHE;

/**
 * 智能体仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class AgentInfoRepositoryImpl extends ServiceImpl<AgentInfoMapper, AgentInfoPO> {

    private final AgentInfoMapper agentInfoMapper;
    private final AgentInfoAssembler agentInfoAssembler;

    @Cache(value = AGENT_TENANT_ID_CACHE, fieldKey = "#workflowId", expireTime = 0)
    @IgnoreTenant
    public Long getTenantIdByWorkflowId(String workflowId) {
        AgentInfoPO agentInfoPO = agentInfoMapper.selectOne(new LambdaQueryWrapper<AgentInfoPO>()
            .eq(AgentInfoPO::getWorkflowId, workflowId));
        if (agentInfoPO == null) {
            return 0L;
        }
        return agentInfoPO.getTenantId();
    }

    /**
     * 根据查询条件和智能体ID列表执行分页查询
     *
     * @param query 查询条件
     * @param agentIds 智能体ID列表，可以为null
     * @return 智能体DTO分页对象
     */
    @AuthSqlInject
    public PageResultDTO<AgentInfoDTO> pageByIds(AgentInfoQuery query, Set<Long> agentIds) {
        // 构建基本查询条件
        LambdaQueryWrapper<AgentInfoPO> queryWrapper = buildQueryWrapper(query);
        queryWrapper.in(CollUtil.isNotEmpty(agentIds), AgentInfoPO::getId, agentIds);
        queryWrapper.orderByDesc(AgentInfoPO::getUpdateTime);
        queryWrapper.orderByDesc(AgentInfoPO::getId);
        Page<AgentInfoPO> page = agentInfoMapper.selectPage(
            new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper);
        return agentInfoAssembler.toPageResult(page);
    }

    /**
     * 根据ID获取智能体
     *
     * @param id 智能体ID
     * @return 智能体DTO
     */
    public AgentInfoDTO getAgent(Long id) {
        AgentInfoPO agentInfoPO = agentInfoMapper.selectById(id);
        if (agentInfoPO == null) {
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST);
        }

        return agentInfoAssembler.PO2DTO(agentInfoPO);
    }


    public AgentInfoDTO getAgentByWorkflowId(String workflowId) {
        AgentInfoPO agentInfoPO = agentInfoMapper.selectOne(new LambdaQueryWrapper<AgentInfoPO>()
            .eq(AgentInfoPO::getWorkflowId, workflowId)
            .eq(AgentInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED));
        if (agentInfoPO == null) {
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST);
        }
        return agentInfoAssembler.PO2DTO(agentInfoPO);
    }

    /**
     * 创建智能体
     *
     * @param agentInfoPO 智能体PO
     * @return 智能体ID
     */
    public Long createAgent(AgentInfoPO agentInfoPO) {
        agentInfoMapper.insert(agentInfoPO);
        return agentInfoPO.getId();
    }

    /**
     * 更新智能体
     *
     * @param agentInfoPO 智能体PO
     * @return 是否更新成功
     */
    public Boolean updateAgent(AgentInfoPO agentInfoPO) {
        // 检查智能体是否存在
        AgentInfoPO existingAgent = agentInfoMapper.selectById(agentInfoPO.getId());
        if (existingAgent == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        if(existingAgent.getTeamId() != null) {
            agentInfoPO.setTeamId(existingAgent.getTeamId());
        }
        int result = agentInfoMapper.updateById(agentInfoPO);
        return result > 0;
    }

    /**
     * 删除智能体,逻辑删除
     *
     * @param id 智能体ID
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteAgent(Long id) {
        AgentInfoPO agentInfoPO = new AgentInfoPO();
        agentInfoPO.setId(id);
        agentInfoPO.setDeleted(DeletedTypeEnum.DELETED);
        return agentInfoMapper.updateById(agentInfoPO) > 0;
    }

    /**
     * 根据查询条件构建查询对象
     *
     * @param query 查询条件
     * @return 查询对象
     */
    private LambdaQueryWrapper<AgentInfoPO> buildQueryWrapper(AgentInfoQuery query) {
        LambdaQueryWrapper<AgentInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(query.getId() != null, AgentInfoPO::getId, query.getId());
        if (query.getTeamId() != null) {
            queryWrapper.eq(AgentInfoPO::getTeamId, query.getTeamId());
        }else {
            queryWrapper.eq(AgentInfoPO::getCreateUserId, CurrentUserHolder.getUserId());
            queryWrapper.isNull(AgentInfoPO::getTeamId);
        }
        if (Objects.equals(BooleanTypeEnum.TRUE, query.getAll())) {
            queryWrapper.in(AgentInfoPO::getAgentType, List.of(AgentTypeEnum.CHAT, AgentTypeEnum.WORKFLOW));
        }else {
            queryWrapper.eq(AgentInfoPO::getAgentType, query.getAgentType());
        }
        queryWrapper.eq(CharSequenceUtil.isNotBlank(query.getWorkflowId()), AgentInfoPO::getWorkflowId, query.getWorkflowId());
        queryWrapper.like(CharSequenceUtil.isNotBlank(query.getName()), AgentInfoPO::getName, query.getName());
        queryWrapper.eq(AgentInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return queryWrapper;
    }

    @IgnoreTenant
    public List<VerifyAgentDTO> getValidAgent() {
        List<AgentInfoPO> agentInfoPOS = agentInfoMapper.selectList(new LambdaQueryWrapper<AgentInfoPO>()
            .eq(AgentInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED)
            .isNotNull(AgentInfoPO::getExtraConfig));
        if (CollUtil.isEmpty(agentInfoPOS)) {
           return Lists.newArrayList();
        }
        return agentInfoAssembler.convertVAD(agentInfoPOS);

    }

    @IgnoreTenant
    public AgentInfoPO findByIdIgnoreTenant(Long agentId) {
        return getById(agentId);
    }
}
