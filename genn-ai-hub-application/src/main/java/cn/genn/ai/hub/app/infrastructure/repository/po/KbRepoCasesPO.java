package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * KbRepoCasesPO对象
 *
 * <AUTHOR>
 * @desc 案例库
 */
@Data
@TableName(value = "kb_repo_cases", autoResultMap = true)
public class KbRepoCasesPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 知识库ID，标识集合所属的数据库（关联 repo 表）
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 标签
     */
    @TableField("tag")
    private String tag;

    /**
     * 分类
     */
    @TableField("category")
    private String category;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 案例详情
     */
    @TableField("content")
    private String content;

    /**
     * 影响内容
     */
    @TableField("impact")
    private String impact;

    /**
     * 整改措施
     */
    @TableField("rectify")
    private String rectify;

    /**
     * 处理状态,0:WAIT-未处理, 1:PROCESSING-处理中,2:DONE-已处理
     */
    @TableField("handle_status")
    private HandleStatusEnum handleStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

}

