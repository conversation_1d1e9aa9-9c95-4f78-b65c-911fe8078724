package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoTermMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTermPO;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 知识库术语库Repository实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class KbRepoTermRepositoryImpl extends ServiceImpl<KbRepoTermMapper, KbRepoTermPO> {

    @Resource
    private KbRepoTermMapper termMapper;

    public Boolean editTerm(KbRepoTermPO po) {
        KbRepoTermPO termPO = this.getById(po.getId());
        if (termPO == null) {
            return false;
        }
        return this.updateById(po);
    }

    public Boolean updateStatus(KbRepoTermPO po) {
        KbRepoTermPO termPO = this.getById(po.getId());
        if (termPO == null) {
            return false;
        }
        UpdateWrapper<KbRepoTermPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoTermPO::getId, termPO.getId())
            .set(KbRepoTermPO::getEnabled, po.getEnabled());
        return this.update(updateWrapper);
    }

    public Boolean batchInsert(List<KbRepoTermPO> termList) {
        if (termList == null || termList.isEmpty()) {
            return false;
        }
        return this.saveBatch(termList, 10);
    }

    public Boolean batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return this.removeBatchByIds(ids);
    }

    public Long insert(KbRepoTermPO termPO) {
        termMapper.insert(termPO);
        return termPO.getId();
    }

    public KbRepoTermPO getByName(Long repoId, String name) {
        try {
            QueryWrapper<KbRepoTermPO> queryWrapper = new QueryWrapper<>();
            queryWrapper
                .lambda()
                .eq(KbRepoTermPO::getRepoId, repoId)
                .eq(KbRepoTermPO::getName, name)
                .eq(KbRepoTermPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
            List<KbRepoTermPO> list = termMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(list)) {
                return list.get(0);
            }
            return null;
        } catch (Exception e) {
            log.error("getByName error, repoId: {}, name: {}", repoId, name, e);
            throw e;
        }
    }

    public List<KbRepoTermPO> getByNameList(Long repoId, List<String> nameList) {
        try {
            QueryWrapper<KbRepoTermPO> queryWrapper = new QueryWrapper<>();
            queryWrapper
                .lambda()
                .eq(KbRepoTermPO::getRepoId, repoId)
                .in(KbRepoTermPO::getName, nameList)
                .eq(KbRepoTermPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
            return termMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("getByName error, repoId: {}, nameList: {}", repoId, nameList, e);
            throw e;
        }
    }
}
