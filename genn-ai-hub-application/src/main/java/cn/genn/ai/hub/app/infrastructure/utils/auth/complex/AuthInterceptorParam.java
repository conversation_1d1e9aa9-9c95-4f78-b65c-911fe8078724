package cn.genn.ai.hub.app.infrastructure.utils.auth.complex;

import cn.genn.ai.hub.app.infrastructure.config.auth.Auth;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class AuthInterceptorParam {

    /**
     * 鉴权注解
     */
    private Auth auth;

    /**
     * 唯一标识
     */
    private String uniqueId;

    /**
     * key->参数名称
     * v-> 参数值
     * 使用时尽量不要改变参数值中的内容，如果需要改变，该参数对象请实现序列化接口
     */
    private Map<String, Object> paramsMap;
}
