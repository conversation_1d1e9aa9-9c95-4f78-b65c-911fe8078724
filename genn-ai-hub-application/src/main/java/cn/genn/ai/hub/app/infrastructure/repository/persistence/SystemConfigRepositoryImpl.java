package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.SystemConfigMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.SystemConfigPO;
import cn.genn.cache.redis.annotation.Cache;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class SystemConfigRepositoryImpl extends ServiceImpl<SystemConfigMapper, SystemConfigPO>  {

    @Cache(value = CacheConstants.SYSTEM_CONFIG_TYPE_CACHE, fieldKey = "#type")
    public List<SystemConfigPO> listByType(String type) {
        return lambdaQuery()
            .eq(SystemConfigPO::getConfigType, type)
            .eq(SystemConfigPO::getDeleted, DeletedTypeEnum.NOT_DELETED)
            .list();
    }

    @Cache(value = CacheConstants.SYSTEM_CONFIG_KEY_CACHE, fieldKey = "#type + ':' + #key")
    public SystemConfigPO getByTypeAndKey(String type, String key) {
        return lambdaQuery()
            .eq(SystemConfigPO::getConfigType, type)
            .eq(SystemConfigPO::getConfigKey, key)
            .eq(SystemConfigPO::getDeleted, DeletedTypeEnum.NOT_DELETED)
            .one();
    }
}
