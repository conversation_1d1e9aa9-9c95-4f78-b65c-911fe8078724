package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.CollectionStatusEnum;
import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.TrainingModelEnum;
import cn.genn.ai.hub.app.application.enums.TrainingTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * KbRepoCollectionPO对象
 *
 * <AUTHOR>
 * @desc 知识库数据集集合
 */
@Data
@TableName(value = "kb_repo_collection", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class KbRepoCollectionPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 父id
     */
    @TableField("pid")
    private Long pid;

    /**
     * 知识库ID，标识集合所属的数据库（关联 repo 表）
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 集合名称，用户自定义的集合标识名称
     */
    @TableField("name")
    private String name;

    /**
     * 类型, folder:FOLDER-文件夹, file:FILE-文件, web:WEB-网页, manual_text:MANUAL_TEXT-手动文本
     */
    @TableField("collection_type")
    private CollectionTypeEnum collectionType;

    /**
     * 训练模式, manual:MANUAL-手动, auto:AUTO-自动
     */
    @TableField("training_type")
    private TrainingTypeEnum trainingType;

    /**
     * 处理方式 直接分段 增强分段 问答拆分
     */
    @TableField("handle_type")
    private TrainingModelEnum handleType;

    /**
     * 分块大小，文本分割的字符数限制（单位：字符）
     */
    @TableField("chunk_size")
    private Integer chunkSize;

    /**
     * 分块分隔符，用于文本分割的符号（如换行符）
     */
    @TableField("chunk_splitter")
    private String chunkSplitter;

    /**
     * 分块大小，文本分割的字符数限制（单位：字符）
     */
    @TableField("data_size")
    private Integer dataSize;
    /**
     * 处理配置项
     */
    @TableField("handle_config")
    private String handleConfig;

    /**
     * 深度解析开关
     */
    @TableField("deep_parse")
    private Boolean deepParse;

    /**
     * 智能分块摘要开关
     */
    @TableField("smart_chunk_summary")
    private Boolean smartChunkSummary;

    /**
     * 标签列表，用于分类和检索集合
     */
    @TableField("tags")
    private String tags;

    /**
     * 原始链接，网页或外部文件的原始URL
     */
    @TableField("raw_link")
    private String rawLink;

    /**
     * 文件id
     */
    @TableField("file_id")
    private Long fileId;

    /**
     * 外部文件ID，第三方存储系统中的文件标识
     */
    @TableField("external_file_id")
    private String externalFileId;

    /**
     * 飞书深度导入下级文档开关
     */
    @TableField("deep_import")
    private Boolean deepImport;

    /**
     * 外部文件 - 表格sheetID
     */
    @TableField("external_sheet_id")
    private String externalSheetId;

    /**
     * 同步飞书参数
     */
    @TableField("sync_params")
    private String syncParams;

    /**
     * 外部文件类型 docx、sheet、file...
     */
    @TableField("external_file_type")
    private String externalFileType;

    /**
     * API文件ID，通过API上传的文件标识
     */
    @TableField("api_file_id")
    private String apiFileId;

    /**
     * 外部文件URL，动态鉴权的文件访问链接（支持 {{fileId}} 变量）
     */
    @TableField("external_file_url")
    private String externalFileUrl;

    /**
     * 下次同步时间，用于定时同步外部数据源
     */
    @TableField("next_sync_time")
    private LocalDateTime nextSyncTime;

    /**
     * 原始文本长度，记录未分割前的文本总长度（单位：字符）
     */
    @TableField("raw_text_length")
    private Integer rawTextLength;

    /**
     * 原始文本哈希值，用于检测内容变更
     */
    @TableField("hash_raw_text")
    private String hashRawText;

    /**
     * 元数据，包含扩展信息如：webPageSelector（网页选择器）、relatedImgId（关联图片ID）
     */
    @TableField("metadata")
    private String metadata;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 数据集处理状态,wait_segment:WAIT_SEGMENT-等待分片,wait_index:WAIT_INDEX-等待索引,processing:PROCESSING-处理中,completed:COMPLETED-已完成,failed_index:FAILED_INDEX-索引失败,failed_segment:FAILED_SEMENT-分片失败,failed_upload:FAILED_UPLOAD-上传失败
     */
    @TableField("collection_status")
    private CollectionStatusEnum collectionStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

