package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.genn.core.model.enums.DeletedTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * KbRepoFilePO对象
 *
 * <AUTHOR>
 * @desc 知识库文件信息
 */
@Data
@TableName(value = "kb_repo_file", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class KbRepoFilePO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户 id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 关联的知识库 ID
     */
    @TableField("repo_id")
    private Long repoId;

    /**
     * 关联的集合 ID（与 collection 表关联）
     */
    @TableField("collection_id")
    private Long collectionId;

    /**
     * 文件存储标识（本地或第三方存储）
     */
    @TableField("file_platform")
    private String filePlatform;

    /**
     * 外部文件 ID（如 S3/OSS）
     */
    @TableField("external_file_id")
    private String externalFileId;

    /**
     * 外部文件访问 URL（支持动态变量如 {{fileId}}）
     */
    @TableField("external_file_url")
    private String externalFileUrl;

    /**
     * 文件总大小（单位：字节）
     */
    @TableField("length")
    private Integer length;

    /**
     * 原始文件名（含扩展名）
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件类型（如 text/plain, application/pdf）
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 扩展元数据（包含权限信息）
     */
    @TableField("metadata")
    private String metadata;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;

}

