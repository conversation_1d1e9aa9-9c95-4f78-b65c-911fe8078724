package cn.genn.ai.hub.app.infrastructure.converter;

import cn.genn.ai.hub.app.domain.prompt.model.entity.Prompt;
import cn.genn.ai.hub.app.domain.prompt.model.entity.PromptVersion;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptVersionPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 提示词转换器
 * @date 2025-05-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PromptConverter {

    PromptPO toPO(Prompt prompt);

    Prompt toEntity(PromptPO promptPO);

    PromptVersionPO versionToPO(PromptVersion promptVersion);

    List<PromptVersion> versionToEntityList(List<PromptVersionPO> promptVersionPOs);
}
