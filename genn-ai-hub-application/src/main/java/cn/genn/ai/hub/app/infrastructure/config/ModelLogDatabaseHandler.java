package cn.genn.ai.hub.app.infrastructure.config;

import cn.genn.ai.hub.app.application.enums.ModelRequestSource;
import cn.genn.ai.hub.app.infrastructure.converter.ModelLogConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ModelLogMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelLogPO;
import cn.genn.spring.boot.starter.ai.component.log.ModelLog;
import cn.genn.spring.boot.starter.ai.component.log.ModelLogHandler;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 模型调用日志处理器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ModelLogDatabaseHandler implements ModelLogHandler {

    private final ModelLogMapper modelLogMapper;
    private final ModelLogConverter modelLogConverter;
    private final AgentInfoRepositoryImpl agentInfoRepository;
    private final KbRepoBaseInfoRepositoryImpl kbRepoBaseInfoRepository;

    @Override
    public void handleModelLog(ModelLog modelLog) {
        ModelLogPO modelLogPO = modelLogConverter.toModelLogPO(modelLog);
        if (StringUtils.isNotBlank(modelLog.getUserId())) {
            modelLogPO.setCreateUserId(Long.parseLong(modelLog.getUserId()));
            modelLogPO.setCreateUserName(modelLog.getUserName());
        }
        if (ModelRequestSource.WORKFLOW.getCode().equals(modelLog.getRequestSource())) {
            String workflowId = modelLogPO.getRequestKey();
            Long tenantId = agentInfoRepository.getTenantIdByWorkflowId(workflowId);
            modelLogPO.setTenantId(tenantId);
        } else if (ModelRequestSource.KNOWLEDGE.getCode().equals(modelLog.getRequestSource())) {
            String knowledgeId = modelLogPO.getRequestKey();
            if (CharSequenceUtil.isNotEmpty(knowledgeId)) {
                Long tenantId = kbRepoBaseInfoRepository.getTenantIdByKnowledgeId(Long.parseLong(knowledgeId));
                modelLogPO.setTenantId(tenantId);
            }
        } else {
            modelLogPO.setTenantId(0L);
        }
        modelLogMapper.insert(modelLogPO);
    }
}
