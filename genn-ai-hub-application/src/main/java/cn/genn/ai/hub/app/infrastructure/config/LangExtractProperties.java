package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 语言抽取服务配置
 * @date 2025-08-28
 */
@Data
public class LangExtractProperties {

    /**
     * 语言抽取服务域名
     */
    private String domain = "http://localhost:8080";

    /**
     * 语言抽取API路径
     */
    private String path = "/api/extract";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 获取完整的API URL
     */
    public String getApiUrl() {
        return domain + path;
    }
}