package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "genn.event.rocketmq.naming.topic")
public class MQSendTopicConfigs {

    private String gennAiHubAppTaskAnalysis;

}
