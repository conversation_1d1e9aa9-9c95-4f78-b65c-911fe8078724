package cn.genn.ai.hub.app.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * KnowledgeGapAnalysisPO对象
 *
 * <AUTHOR>
 * @desc 知识缺口分析统计表
 */
@Data
@TableName(value = "question_gap_analysis", autoResultMap = true)
public class QuestionGapAnalysisPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 关联问题id
     */
    @TableField("question_id")
    private Long questionId;

    /**
     * 工作流id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 会话的关联id
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 一次问答关联id
     */
    @TableField("task_id")
    private String taskId;


    /**
     * 来源
     * test = '在线调试',
     * online = '在线使用',
     * share = '外部链接调用',
     * api = 'API 调用',
     * cronJob = '定时执行',
     * team = '团队空间对话',
     * feishu = '飞书',
     * official_account = '公众号',
     * wecom = '企业微信'
     */
    @TableField("source")
    private String source;

    /**
     * 知识缺口标签(类型)
     */
    @TableField("type")
    private String type;

    /**
     * 缺失内容(json)
     */
    @TableField("missing_points")
    private String missingPoints;

    /**
     * 缺失内容总结
     */
    @TableField("summary")
    private String summary;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

