package cn.genn.ai.hub.app;

import cn.genn.ai.hub.app.domain.auth.model.valobj.FieldWhereExpression;
import cn.genn.ai.hub.app.domain.auth.model.valobj.OperatorEnum;
import cn.genn.ai.hub.app.infrastructure.utils.auth.SqlUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FieldWhereExpressionTest {

    public static void main(String[] args) {
        // 运行所有测试用例
        testSimpleCondition();
        testAndCondition();
        testOrCondition();
        testNestedAndOr();
        testComplexNestedCondition();
        testDeepNestedCondition();
    }

    /**
     * 测试用例1: 简单条件
     * 输出: name = 'John'
     */
    private static void testSimpleCondition() {
        FieldWhereExpression expr = new FieldWhereExpression();
        expr.setField("name");
        expr.setOperator(OperatorEnum.EQ);
        expr.setValues(List.of("John"));

        String sql = SqlUtils.toWhereSql(expr);
        System.out.println("测试用例1 (简单条件): " + sql);
    }

    /**
     * 测试用例2: AND条件
     * 输出: (name = 'John' AND age > 25)
     */
    private static void testAndCondition() {
        FieldWhereExpression nameExpr = new FieldWhereExpression();
        nameExpr.setField("name");
        nameExpr.setOperator(OperatorEnum.EQ);
        nameExpr.setValues(List.of("John"));

        FieldWhereExpression ageExpr = new FieldWhereExpression();
        ageExpr.setField("age");
        ageExpr.setOperator(OperatorEnum.GT);
        ageExpr.setValues(List.of(25));

        FieldWhereExpression andExpr = new FieldWhereExpression();
        andExpr.setOperator(OperatorEnum.AND);
        andExpr.setContents(Arrays.asList(nameExpr, ageExpr));

        String sql = SqlUtils.toWhereSql(andExpr);
        System.out.println("测试用例2 (AND条件): " + sql);
    }

    /**
     * 测试用例3: OR条件
     * 输出: (status = 'active' OR status = 'pending')
     */
    private static void testOrCondition() {
        FieldWhereExpression statusActive = new FieldWhereExpression();
        statusActive.setField("status");
        statusActive.setOperator(OperatorEnum.EQ);
        statusActive.setValues(List.of("active"));

        FieldWhereExpression statusPending = new FieldWhereExpression();
        statusPending.setField("status");
        statusPending.setOperator(OperatorEnum.EQ);
        statusPending.setValues(List.of("pending"));

        FieldWhereExpression orExpr = new FieldWhereExpression();
        orExpr.setOperator(OperatorEnum.OR);
        orExpr.setContents(Arrays.asList(statusActive, statusPending));

        String sql = SqlUtils.toWhereSql(orExpr);
        System.out.println("测试用例3 (OR条件): " + sql);
    }

    /**
     * 测试用例4: 嵌套AND-OR
     * 输出: (name = 'John' AND (department = 'IT' OR department = 'HR'))
     */
    private static void testNestedAndOr() {
        FieldWhereExpression nameExpr = new FieldWhereExpression();
        nameExpr.setField("name");
        nameExpr.setOperator(OperatorEnum.EQ);
        nameExpr.setValues(List.of("John"));

        FieldWhereExpression deptIT = new FieldWhereExpression();
        deptIT.setField("department");
        deptIT.setOperator(OperatorEnum.EQ);
        deptIT.setValues(List.of("IT"));

        FieldWhereExpression deptHR = new FieldWhereExpression();
        deptHR.setField("department");
        deptHR.setOperator(OperatorEnum.EQ);
        deptHR.setValues(List.of("HR"));

        // department = 'IT' OR department = 'HR'
        FieldWhereExpression deptOrExpr = new FieldWhereExpression();
        deptOrExpr.setOperator(OperatorEnum.OR);
        deptOrExpr.setContents(Arrays.asList(deptIT, deptHR));

        // name = 'John' AND (department = 'IT' OR department = 'HR')
        FieldWhereExpression andExpr = new FieldWhereExpression();
        andExpr.setOperator(OperatorEnum.AND);
        andExpr.setContents(Arrays.asList(nameExpr, deptOrExpr));

        String sql = SqlUtils.toWhereSql(andExpr);
        System.out.println("测试用例4 (嵌套AND-OR): " + sql);
    }

    /**
     * 测试用例5: 复杂嵌套条件
     * 输出: ((name = 'John' AND age > 25) OR (status IN ('active', 'pending') AND create_time > '2023-01-01'))
     */
    private static void testComplexNestedCondition() {
        // name = 'John'
        FieldWhereExpression nameExpr = new FieldWhereExpression();
        nameExpr.setField("name");
        nameExpr.setOperator(OperatorEnum.EQ);
        nameExpr.setValues(List.of("John"));

        // age > 25
        FieldWhereExpression ageExpr = new FieldWhereExpression();
        ageExpr.setField("age");
        ageExpr.setOperator(OperatorEnum.GT);
        ageExpr.setValues(List.of(25));

        // status IN ('active', 'pending')
        FieldWhereExpression statusExpr = new FieldWhereExpression();
        statusExpr.setField("status");
        statusExpr.setOperator(OperatorEnum.IN);
        statusExpr.setValues(Arrays.asList("active", "pending"));

        // create_time > '2023-01-01'
        FieldWhereExpression createTimeExpr = new FieldWhereExpression();
        createTimeExpr.setField("create_time");
        createTimeExpr.setOperator(OperatorEnum.GT);
        createTimeExpr.setValues(List.of("2023-01-01"));

        // name = 'John' AND age > 25
        FieldWhereExpression condition1 = new FieldWhereExpression();
        condition1.setOperator(OperatorEnum.AND);
        condition1.setContents(Arrays.asList(nameExpr, ageExpr));

        // status IN ('active', 'pending') AND create_time > '2023-01-01'
        FieldWhereExpression condition2 = new FieldWhereExpression();
        condition2.setOperator(OperatorEnum.AND);
        condition2.setContents(Arrays.asList(statusExpr, createTimeExpr));

        // (name = 'John' AND age > 25) OR (status IN ('active', 'pending') AND create_time > '2023-01-01')
        FieldWhereExpression rootExpr = new FieldWhereExpression();
        rootExpr.setOperator(OperatorEnum.OR);
        rootExpr.setContents(Arrays.asList(condition1, condition2));

        String sql = SqlUtils.toWhereSql(rootExpr);
        System.out.println("测试用例5 (复杂嵌套条件): " + sql);
    }

    /**
     * 测试用例6: 深度嵌套条件
     * 输出: (name = 'John' AND ((age BETWEEN 20 AND 30 AND department = 'IT') OR (status IN ('active', 'pending') AND (is_deleted = 0 OR create_time > '2023-01-01'))))
     */
    private static void testDeepNestedCondition() {
        // name = 'John'
        FieldWhereExpression nameExpr = new FieldWhereExpression();
        nameExpr.setField("name");
        nameExpr.setOperator(OperatorEnum.EQ);
        nameExpr.setValues(List.of("John"));

        // age BETWEEN 20 AND 30
        FieldWhereExpression ageExpr = new FieldWhereExpression();
        ageExpr.setField("age");
        ageExpr.setOperator(OperatorEnum.BETWEEN);
        ageExpr.setValues(Arrays.asList(20, 30));

        // department = 'IT'
        FieldWhereExpression deptExpr = new FieldWhereExpression();
        deptExpr.setField("department");
        deptExpr.setOperator(OperatorEnum.EQ);
        deptExpr.setValues(List.of("IT"));

        // status IN ('active', 'pending')
        FieldWhereExpression statusExpr = new FieldWhereExpression();
        statusExpr.setField("status");
        statusExpr.setOperator(OperatorEnum.IN);
        statusExpr.setValues(Arrays.asList("active", "pending"));

        // is_deleted = 0
        FieldWhereExpression isDeletedExpr = new FieldWhereExpression();
        isDeletedExpr.setField("is_deleted");
        isDeletedExpr.setOperator(OperatorEnum.EQ);
        isDeletedExpr.setValues(List.of(0));

        // create_time > '2023-01-01'
        FieldWhereExpression createTimeExpr = new FieldWhereExpression();
        createTimeExpr.setField("create_time");
        createTimeExpr.setOperator(OperatorEnum.GT);
        createTimeExpr.setValues(List.of("2023-01-01"));

        // age BETWEEN 20 AND 30 AND department = 'IT'
        FieldWhereExpression condition1 = new FieldWhereExpression();
        condition1.setOperator(OperatorEnum.AND);
        condition1.setContents(Arrays.asList(ageExpr, deptExpr));

        // is_deleted = 0 OR create_time > '2023-01-01'
        FieldWhereExpression condition2Inner = new FieldWhereExpression();
        condition2Inner.setOperator(OperatorEnum.OR);
        condition2Inner.setContents(Arrays.asList(isDeletedExpr, createTimeExpr));

        // status IN ('active', 'pending') AND (is_deleted = 0 OR create_time > '2023-01-01')
        FieldWhereExpression condition2 = new FieldWhereExpression();
        condition2.setOperator(OperatorEnum.AND);
        condition2.setContents(Arrays.asList(statusExpr, condition2Inner));

        // (age BETWEEN 20 AND 30 AND department = 'IT') OR (status IN ('active', 'pending') AND (is_deleted = 0 OR create_time > '2023-01-01'))
        FieldWhereExpression orCondition = new FieldWhereExpression();
        orCondition.setOperator(OperatorEnum.OR);
        orCondition.setContents(Arrays.asList(condition1, condition2));

        // name = 'John' AND ((age BETWEEN 20 AND 30 AND department = 'IT') OR (status IN ('active', 'pending') AND (is_deleted = 0 OR create_time > '2023-01-01')))
        FieldWhereExpression rootExpr = new FieldWhereExpression();
        rootExpr.setOperator(OperatorEnum.AND);
        rootExpr.setContents(Arrays.asList(nameExpr, orCondition));

        String sql = SqlUtils.toWhereSql(rootExpr);
        System.out.println("测试用例6 (深度嵌套条件): " + sql);
    }
}
