package cn.genn.ai.hub.app;

import cn.genn.ai.hub.app.application.processor.handler.FileParseTaskHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Date: 2025/4/21
 * @Author: kangjian
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class TaskTest {

    @Autowired
    private FileParseTaskHandler fileParseTaskHandler;


    @Test
    public void test() {
        fileParseTaskHandler.handleTask("17328", "1");
    }
}
